#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
动态翻页处理模块
处理JavaScript点击翻页、无限滚动、下拉选择等动态翻页模式
"""

import time
import json
from typing import Dict, List, Any, Optional, Tuple
from playwright.sync_api import sync_playwright, Page, Browser, BrowserContext

print("Loaded dynamic_pagination_handler.py from:", __file__)


class DynamicPaginationHandler:
    """动态翻页处理器"""
    
    def __init__(self, headless: bool = True, timeout: int = 30000):
        """
        初始化动态翻页处理器
        
        Args:
            headless: 是否无头模式运行浏览器
            timeout: 页面加载超时时间(毫秒)
        """
        self.headless = headless
        self.timeout = timeout
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start_browser()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_browser()
    
    def start_browser(self):
        """启动浏览器"""
        if not self.playwright:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.launch(headless=self.headless)
            self.context = self.browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            )
            self.page = self.context.new_page()
            self.page.set_default_timeout(self.timeout)
    
    def close_browser(self):
        """关闭浏览器"""
        if self.page:
            self.page.close()
        if self.context:
            self.context.close()
        if self.browser:
            self.browser.close()
        if self.playwright:
            self.playwright.stop()
    
    def handle_javascript_click_pagination(self, url: str, max_pages: int = 5) -> List[Dict[str, Any]]:
        """
        处理JavaScript点击翻页
        
        Args:
            url: 起始URL
            max_pages: 最大翻页数
            
        Returns:
            每页的数据列表
        """
        if not self.page:
            self.start_browser()
        
        results = []
        
        try:
            # 访问起始页面
            self.page.goto(url)
            self.page.wait_for_load_state('networkidle')
            
            for page_num in range(1, max_pages + 1):
                print(f"正在处理第 {page_num} 页...")
                
                # 提取当前页面内容
                page_data = self._extract_page_content()
                page_data['page_number'] = page_num
                page_data['url'] = self.page.url
                results.append(page_data)
                
                if page_num < max_pages:
                    # 尝试点击下一页
                    if not self._click_next_page():
                        print(f"无法找到下一页按钮，停止在第 {page_num} 页")
                        break
                    
                    # 等待页面加载
                    time.sleep(2)
                    self.page.wait_for_load_state('networkidle')
        
        except Exception as e:
            print(f"JavaScript点击翻页处理出错: {e}")
        
        return results
    
    def handle_infinite_scroll_pagination(self, url: str, max_scrolls: int = 10) -> Dict[str, Any]:
        """
        处理无限滚动翻页
        
        Args:
            url: 起始URL
            max_scrolls: 最大滚动次数
            
        Returns:
            所有加载的内容数据
        """
        if not self.page:
            self.start_browser()
        
        try:
            # 访问起始页面
            self.page.goto(url)
            self.page.wait_for_load_state('networkidle')
            
            scroll_count = 0
            last_content_length = 0
            
            while scroll_count < max_scrolls:
                print(f"正在进行第 {scroll_count + 1} 次滚动...")
                
                # 滚动到页面底部
                self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                
                # 等待新内容加载
                time.sleep(2)
                
                # 检查是否有新内容加载
                current_content = self._extract_page_content()
                current_content_length = len(current_content.get('articles', []))
                
                if current_content_length == last_content_length:
                    print("没有新内容加载，停止滚动")
                    break
                
                last_content_length = current_content_length
                scroll_count += 1
                
                # 检查是否有加载指示器
                loading_indicators = [
                    '.paged-scroll-loading',
                    '.loading',
                    '.spinner',
                    'img[src*="more"]'
                ]
                
                for indicator in loading_indicators:
                    try:
                        if self.page.is_visible(indicator):
                            print("检测到加载指示器，等待加载完成...")
                            self.page.wait_for_selector(indicator, state='hidden', timeout=5000)
                            break
                    except:
                        continue
            
            # 提取最终内容
            final_data = self._extract_page_content()
            final_data['url'] = url
            final_data['scroll_count'] = scroll_count
            
            return final_data
        
        except Exception as e:
            print(f"无限滚动翻页处理出错: {e}")
            return {'error': str(e), 'url': url}
    
    def handle_dropdown_pagination(self, url: str, max_pages: int = 5) -> List[Dict[str, Any]]:
        """
        处理下拉选择翻页
        
        Args:
            url: 起始URL
            max_pages: 最大翻页数
            
        Returns:
            每页的数据列表
        """
        if not self.page:
            self.start_browser()
        
        results = []
        
        try:
            # 访问起始页面
            self.page.goto(url)
            self.page.wait_for_load_state('networkidle')
            
            # 查找页面选择下拉框
            page_selectors = [
                'select[name*="page"]',
                '.page-select',
                '#pageSelect',
                'select[onchange*="page"]'
            ]
            
            page_select = None
            for selector in page_selectors:
                try:
                    if self.page.is_visible(selector):
                        page_select = selector
                        break
                except:
                    continue
            
            if not page_select:
                print("未找到页面选择下拉框")
                return results
            
            # 获取可用的页面选项
            options = self.page.evaluate(f"""
                Array.from(document.querySelector('{page_select}').options)
                     .map(option => ({{value: option.value, text: option.text}}))
            """)
            
            for i, option in enumerate(options[:max_pages]):
                print(f"正在处理第 {i + 1} 页 (选项: {option['text']})...")
                
                # 选择页面
                self.page.select_option(page_select, option['value'])
                
                # 查找并点击确认按钮
                go_buttons = ['.go-btn', 'input[type="submit"]', 'button[onclick*="page"]']
                for btn_selector in go_buttons:
                    try:
                        if self.page.is_visible(btn_selector):
                            self.page.click(btn_selector)
                            break
                    except:
                        continue
                
                # 等待页面加载
                time.sleep(2)
                self.page.wait_for_load_state('networkidle')
                
                # 提取页面内容
                page_data = self._extract_page_content()
                page_data['page_number'] = i + 1
                page_data['page_option'] = option
                page_data['url'] = self.page.url
                results.append(page_data)
        
        except Exception as e:
            print(f"下拉选择翻页处理出错: {e}")
        
        return results
    
    def _extract_page_content(self) -> Dict[str, Any]:
        """
        提取页面内容
        
        Returns:
            页面内容字典
        """
        try:
            # 通用内容提取选择器
            content_selectors = {
                'articles': [
                    'article',
                    '.article',
                    '.news-item',
                    '.list-item',
                    '.content-item',
                    'li a[href]',
                    '.title a'
                ],
                'titles': [
                    'h1', 'h2', 'h3',
                    '.title',
                    '.headline',
                    'a[title]'
                ],
                'links': [
                    'a[href]'
                ]
            }
            
            extracted_data = {}
            
            for data_type, selectors in content_selectors.items():
                extracted_data[data_type] = []
                
                for selector in selectors:
                    try:
                        elements = self.page.query_selector_all(selector)
                        for element in elements:
                            if data_type == 'articles':
                                text = element.text_content()
                                href = element.get_attribute('href')
                                if text and text.strip():
                                    extracted_data[data_type].append({
                                        'text': text.strip(),
                                        'href': href
                                    })
                            elif data_type == 'titles':
                                text = element.text_content()
                                if text and text.strip():
                                    extracted_data[data_type].append(text.strip())
                            elif data_type == 'links':
                                href = element.get_attribute('href')
                                text = element.text_content()
                                if href:
                                    extracted_data[data_type].append({
                                        'href': href,
                                        'text': text.strip() if text else ''
                                    })
                        
                        if extracted_data[data_type]:
                            break  # 如果找到内容就停止尝试其他选择器
                    except:
                        continue
            
            return extracted_data
        
        except Exception as e:
            print(f"内容提取出错: {e}")
            return {'error': str(e)}
    
    def _click_next_page(self) -> bool:
        """
        点击下一页按钮
        
        Returns:
            是否成功点击
        """
        next_selectors = [
            '.next:not(.lose)',  # 排除禁用的下一页按钮
            'a[onclick*="page"]:contains("下一页")',
            '.js_pageI:not(.cur) + .js_pageI',  # 当前页的下一个页码
            '.pager a:contains("下一页")',
            'a:contains("下一页")',
            '.next-page'
        ]
        
        for selector in next_selectors:
            try:
                if ':contains(' in selector:
                    # 处理包含文本的选择器
                    elements = self.page.query_selector_all('a')
                    for element in elements:
                        if '下一页' in element.text_content():
                            element.click()
                            return True
                else:
                    if self.page.is_visible(selector):
                        self.page.click(selector)
                        return True
            except Exception as e:
                print(f"尝试点击选择器 {selector} 失败: {e}")
                continue
        
        return False


# 全局实例
dynamic_pagination_handler = DynamicPaginationHandler()


# 兼容性函数
def handle_dynamic_pagination(url: str, pagination_type: str, **kwargs) -> Any:
    """
    处理动态翻页的统一接口
    
    Args:
        url: 目标URL
        pagination_type: 翻页类型
        **kwargs: 其他参数
        
    Returns:
        翻页结果
    """
    with DynamicPaginationHandler() as handler:
        if pagination_type == 'javascript_click':
            return handler.handle_javascript_click_pagination(url, kwargs.get('max_pages', 5))
        elif pagination_type == 'infinite_scroll':
            return handler.handle_infinite_scroll_pagination(url, kwargs.get('max_scrolls', 10))
        elif pagination_type == 'dropdown_pagination':
            return handler.handle_dropdown_pagination(url, kwargs.get('max_pages', 5))
        else:
            raise ValueError(f"不支持的动态翻页类型: {pagination_type}")


if __name__ == "__main__":
    # 测试代码
    print("动态翻页处理模块已加载")

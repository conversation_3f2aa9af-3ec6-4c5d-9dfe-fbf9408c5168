#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试翻页配置传递问题
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_pagination_logic():
    """调试翻页逻辑"""
    print("=" * 60)
    print("调试翻页配置传递问题")
    print("=" * 60)
    
    # 模拟用户的配置
    test_configs = [
        {
            "name": "滚动翻页配置（应该使用动态翻页）",
            "pagination_config": {
                'enabled': True,
                'pagination_type': '滚动翻页',
                'scroll_container_selector': '#largeData',
                'scroll_step': 800,
                'scroll_delay': 2000,
                'max_scrolls': 20,
                'load_element_pattern': '#load{n}'
            }
        },
        {
            "name": "禁用动态翻页配置（应该使用传统翻页）",
            "pagination_config": {
                'enabled': False,
                'pagination_type': '禁用动态翻页'
            }
        },
        {
            "name": "空配置（应该使用传统翻页）",
            "pagination_config": {}
        }
    ]
    
    for config in test_configs:
        print(f"\n" + "=" * 40)
        print(f"测试配置: {config['name']}")
        print("=" * 40)
        
        pagination_config = config.get('pagination_config', {})
        print(f"pagination_config: {pagination_config}")
        
        # 模拟 CrawlerThread.run() 中的判断逻辑
        condition1 = pagination_config.get('enabled', False)
        condition2 = pagination_config.get('pagination_type') != '禁用动态翻页'
        
        print(f"enabled: {condition1}")
        print(f"pagination_type: {pagination_config.get('pagination_type')}")
        print(f"pagination_type != '禁用动态翻页': {condition2}")
        
        if condition1 and condition2:
            print("✅ 结果: 使用动态翻页模式，调用PaginationHandler...")
            
            # 进一步检查翻页类型
            pagination_type = pagination_config.get('pagination_type')
            if pagination_type == '滚动翻页':
                print("  → 将调用滚动翻页功能")
            elif pagination_type == '点击翻页':
                print("  → 将调用点击翻页功能")
            else:
                print(f"  → 未知翻页类型: {pagination_type}")
        else:
            print("❌ 结果: 使用传统翻页模式...")
            print("  → 将调用 crawler.crawl_articles()")
            
            # 检查为什么条件失败
            if not condition1:
                print("  原因: pagination_config.enabled = False")
            if not condition2:
                print(f"  原因: pagination_type = '{pagination_config.get('pagination_type')}' 等于 '禁用动态翻页'")

def debug_dynamic_pagination_type_mapping():
    """调试动态翻页类型映射"""
    print("\n" + "=" * 60)
    print("调试动态翻页类型映射")
    print("=" * 60)
    
    # 模拟 get_dynamic_pagination_type_from_config 函数
    def get_dynamic_pagination_type_from_config(pagination_config):
        """从新的翻页配置转换为旧的动态翻页类型"""
        if not pagination_config or not pagination_config.get('enabled', False):
            return None

        pagination_type = pagination_config.get('pagination_type', '禁用动态翻页')

        # 转换映射
        type_map = {
            "禁用动态翻页": None,
            "点击翻页": "javascript_click",
            "滚动翻页": "infinite_scroll",
            "iframe翻页": "javascript_click"  # iframe翻页也使用点击方式
        }

        return type_map.get(pagination_type, None)
    
    test_configs = [
        {'enabled': True, 'pagination_type': '滚动翻页'},
        {'enabled': True, 'pagination_type': '点击翻页'},
        {'enabled': False, 'pagination_type': '滚动翻页'},
        {'enabled': True, 'pagination_type': '禁用动态翻页'},
    ]
    
    for config in test_configs:
        result = get_dynamic_pagination_type_from_config(config)
        print(f"配置: {config}")
        print(f"映射结果: {result}")
        
        # 检查 crawler.py 中的逻辑
        if result and result != "traditional":
            print("  → crawler.py 会显示: '动态翻页应由GUI直接调用PaginationHandler处理，这里使用传统分页方式'")
        else:
            print("  → crawler.py 会正常使用传统分页")
        print()

def debug_user_issue():
    """调试用户遇到的具体问题"""
    print("\n" + "=" * 60)
    print("调试用户问题")
    print("=" * 60)
    
    print("用户报告:")
    print("- GUI 选了无限滚动翻页 却成了传统分页")
    print("- 日志显示: '使用传统翻页模式...'")
    print("- 日志显示: '动态翻页应由GUI直接调用PaginationHandler处理，这里使用传统分页方式'")
    
    print("\n分析:")
    print("1. '使用传统翻页模式...' 来自 CrawlerThread.run() 第36行")
    print("   说明条件判断失败: pagination_config.get('enabled', False) and pagination_config.get('pagination_type') != '禁用动态翻页'")
    
    print("\n2. '动态翻页应由GUI直接调用PaginationHandler处理...' 来自 crawler.py 第791行")
    print("   说明 dynamic_pagination_type 不是 None 也不是 'traditional'")
    
    print("\n可能的原因:")
    print("A. pagination_config 没有正确传递到 CrawlerThread")
    print("B. pagination_config.enabled = False")
    print("C. pagination_config.pagination_type = '禁用动态翻页'")
    print("D. 配置保存/加载有问题")
    
    print("\n建议检查:")
    print("1. 检查 GUI 中动态翻页标签页的配置是否正确保存")
    print("2. 检查 get_current_config() 是否包含正确的 pagination_config")
    print("3. 检查 CrawlerThread 接收到的 config 参数")

if __name__ == "__main__":
    debug_pagination_logic()
    debug_dynamic_pagination_type_mapping()
    debug_user_issue()
    
    print("\n" + "=" * 60)
    print("调试建议")
    print("=" * 60)
    print("1. 在 CrawlerThread.run() 中添加调试输出:")
    print("   print(f'pagination_config: {pagination_config}')")
    print("   print(f'enabled: {pagination_config.get(\"enabled\", False)}')")
    print("   print(f'pagination_type: {pagination_config.get(\"pagination_type\")}')")
    
    print("\n2. 在 start_crawler() 中添加调试输出:")
    print("   print(f'crawler_config pagination_config: {crawler_config.get(\"pagination_config\")}')")
    print("   print(f'dynamic_pagination_type: {crawler_config.get(\"dynamic_pagination_type\")}')")
    
    print("\n3. 检查动态翻页标签页的配置是否正确保存到配置文件")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI中PaginationHandler修复后的功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_import():
    """测试GUI导入"""
    print("=" * 50)
    print("测试GUI导入")
    print("=" * 50)
    
    try:
        import crawler_gui_2
        print("✅ GUI导入成功！")
        
        # 测试CrawlerThread类
        from crawler_gui_2 import CrawlerThread
        print("✅ CrawlerThread类导入成功！")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pagination_handler_access():
    """测试PaginationHandler访问"""
    print("=" * 50)
    print("测试PaginationHandler访问")
    print("=" * 50)
    
    try:
        import PaginationHandler
        print("✅ PaginationHandler模块导入成功！")
        
        # 测试类访问
        handler_class = PaginationHandler.PaginationHandler
        print("✅ PaginationHandler类访问成功！")
        
        # 测试launch_browser函数访问
        launch_browser_func = PaginationHandler.launch_browser
        print("✅ launch_browser函数访问成功！")
        
        return True
        
    except Exception as e:
        print(f"❌ PaginationHandler访问失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crawler_thread_creation():
    """测试CrawlerThread创建"""
    print("=" * 50)
    print("测试CrawlerThread创建")
    print("=" * 50)
    
    try:
        from crawler_gui_2 import CrawlerThread
        
        # 创建测试配置
        test_config = {
            'input_url': 'http://example.com',
            'base_url': 'http://example.com',
            'max_pages': 1,
            'list_container_selector': '.main',
            'article_item_selector': 'a',
            'content_selectors': ['.content'],
            'pagination_config': {
                'enabled': False,
                'pagination_type': '禁用动态翻页'
            }
        }
        
        # 创建CrawlerThread实例
        thread = CrawlerThread(test_config)
        print("✅ CrawlerThread实例创建成功！")
        
        # 测试方法存在
        if hasattr(thread, 'run_dynamic_pagination'):
            print("✅ run_dynamic_pagination方法存在！")
        else:
            print("❌ run_dynamic_pagination方法不存在！")
            
        if hasattr(thread, '_async_dynamic_pagination'):
            print("✅ _async_dynamic_pagination方法存在！")
        else:
            print("❌ _async_dynamic_pagination方法不存在！")
        
        return True
        
    except Exception as e:
        print(f"❌ CrawlerThread创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dynamic_pagination_config():
    """测试动态翻页配置检测"""
    print("=" * 50)
    print("测试动态翻页配置检测")
    print("=" * 50)
    
    try:
        from crawler_gui_2 import CrawlerThread
        
        # 测试启用动态翻页的配置
        dynamic_config = {
            'input_url': 'http://example.com',
            'pagination_config': {
                'enabled': True,
                'pagination_type': 'JavaScript点击',
                'next_button_selector': 'a.next'
            },
            'dynamic_pagination_type': 'javascript_click'
        }
        
        thread = CrawlerThread(dynamic_config)
        print("✅ 动态翻页配置CrawlerThread创建成功！")
        
        # 测试传统翻页配置
        traditional_config = {
            'input_url': 'http://example.com',
            'pagination_config': {
                'enabled': False,
                'pagination_type': '禁用动态翻页'
            },
            'dynamic_pagination_type': 'traditional'
        }
        
        thread2 = CrawlerThread(traditional_config)
        print("✅ 传统翻页配置CrawlerThread创建成功！")
        
        return True
        
    except Exception as e:
        print(f"❌ 动态翻页配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试GUI中PaginationHandler修复...")
    print()
    
    success_count = 0
    total_tests = 4
    
    # 运行各项测试
    if test_gui_import():
        success_count += 1
    print()
    
    if test_pagination_handler_access():
        success_count += 1
    print()
    
    if test_crawler_thread_creation():
        success_count += 1
    print()
    
    if test_dynamic_pagination_config():
        success_count += 1
    print()
    
    print("=" * 50)
    print(f"测试完成！成功: {success_count}/{total_tests}")
    if success_count == total_tests:
        print("🎉 所有测试通过！PaginationHandler修复成功！")
    else:
        print(f"⚠️  有 {total_tests - success_count} 个测试失败，需要进一步检查")
    print("=" * 50)

{"last_used": "上海人大", "groups": {"default": {"input_url": "", "base_url": "", "max_pages": "0", "list_container_selector": "", "article_item_selector": "", "content_selectors": "", "date_selector": "", "source_selector": "", "page_suffix": "index_{n}.html", "url_mode": "input_url"}, "北京人大": {"input_url": "https://www.bjrd.gov.cn/rdzl/rdzc/rdzd/", "base_url": "https://www.bjrd.gov.cn/rdzl/rdzc/rdzd/", "max_pages": "1", "list_container_selector": ".ty_gl.container", "list_container_type": "CSS", "article_item_selector": ".ty_list li", "article_item_type": "CSS", "title_selector": ".xl_title.clearfix h1", "title_selector_type": "CSS", "content_selectors": ".<PERSON><PERSON><PERSON><PERSON>, .view", "content_type": "CSS", "date_selector": ".xl_ly span:first-child", "date_selector_type": "CSS", "source_selector": ".xl_ly span:last-child", "source_selector_type": "CSS", "page_suffix": "index_{n}.html", "page_suffix_start": 1, "url_mode": "relative", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": true, "mode": "balance", "filters": []}, "宁波人大": {"input_url": "https://www.nbrd.gov.cn/col/col1229576425/index.html", "base_url": "https://www.nbrd.gov.cn/col/col1229576425/", "max_pages": "2", "list_container_selector": ".default_pgContainer ul", "list_container_type": "CSS", "article_item_selector": ".default_pgContainer ul li", "article_item_type": "CSS", "title_selector": ".z_title color02", "title_selector_type": "CSS", "content_selectors": ".article_cont", "content_type": "CSS", "date_selector": "//li[contains(@class, \"color02\") and contains(text(), \"时间：", "date_selector_type": "XPath", "source_selector": "//li[contains(@class, \"color02\") and contains(text(), \"来源：", "source_selector_type": "XPath", "page_suffix": "index.html?uid=8022500&pageNum={n}", "page_suffix_start": 2, "url_mode": "relative", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": true, "mode": "balance", "filters": []}, "杭州人大": {"input_url": "https://www.hzrd.gov.cn/col/col1229690487/index.html", "base_url": "https://www.hzrd.gov.cn/col/col1229690487/", "max_pages": "0", "list_container_selector": ".lists_sub_1 ul", "list_container_type": "CSS", "article_item_selector": ".lists_sub_1 ul li", "article_item_type": "CSS", "title_selector": ".article_title", "title_selector_type": "CSS", "content_selectors": ".article_article", "content_type": "CSS", "date_selector": ".article_time span:first-child", "date_selector_type": "CSS", "source_selector": ".article_time span:nth-child(2)", "source_selector_type": "CSS", "page_suffix": "index.html?uid=7856661&pageNum={n}", "page_suffix_start": 2, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "normal", "collect_links": false, "mode": "balance", "filters": []}, "武汉人大": {"input_url": "http://www.whrd.gov.cn/html/rdlz/dbgz/dbjy/index.html", "base_url": "http://www.whrd.gov.cn/html/rdlz/dbgz/dbjy/", "max_pages": "0", "list_container_selector": ".rq-news ul.RQNS-noLine", "list_container_type": "CSS", "article_item_selector": ".rq-news ul.RQNS-noLine li", "article_item_type": "CSS", "title_selector": ".RQVW-title", "title_selector_type": "CSS", "content_selectors": ".RQVW-desc;.canvasWrapper", "content_type": "CSS", "date_selector": ".RQVW-one div:first-child", "date_selector_type": "CSS", "source_selector": ".RQVW-one div:nth-child(2)", "source_selector_type": "CSS", "page_suffix": "list_40_{n}.html", "page_suffix_start": 2, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": false, "mode": "balance", "filters": [], "export_filename": "武汉人大_议案建议.csv", "classid": "3802"}, "武汉人大_议案建议": {"input_url": "http://www.whrd.gov.cn/html/rdlz/dbgz/dbjy/index.html", "base_url": "http://www.whrd.gov.cn/html/rdlz/dbgz/dbjy/", "max_pages": "0", "list_container_selector": ".rq-news ul.RQNS-noLine", "list_container_type": "CSS", "article_item_selector": ".rq-news ul.RQNS-noLine li", "article_item_type": "CSS", "title_selector": ".RQVW-title", "title_selector_type": "CSS", "content_selectors": ".RQVW-desc;.canvasWrapper", "content_type": "CSS", "date_selector": ".RQVW-one div:first-child", "date_selector_type": "CSS", "source_selector": ".RQVW-one div:nth-child(2)", "source_selector_type": "CSS", "page_suffix": "list_40_{n}.html", "page_suffix_start": 2, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": false, "mode": "balance", "filters": [], "export_filename": "武汉人大_议案建议.csv", "classid": "3802", "file_format": "Excel"}, "武汉人大_基层建设": {"input_url": "http://www.whrd.gov.cn/html/rdlz/dbgz/dbhd/index.html", "base_url": "http://www.whrd.gov.cn/html/rdlz/dbgz/dbhd/", "max_pages": "0", "list_container_selector": ".rq-news ul.RQNS-noLine", "list_container_type": "CSS", "article_item_selector": ".rq-news ul.RQNS-noLine li", "article_item_type": "CSS", "title_selector": ".RQVW-title", "title_selector_type": "CSS", "content_selectors": ".RQVW-desc;.canvasWrapper", "content_type": "CSS", "date_selector": ".RQVW-one div:first-child", "date_selector_type": "CSS", "source_selector": ".RQVW-one div:nth-child(2)", "source_selector_type": "CSS", "page_suffix": "list_42_{n}.html", "page_suffix_start": 2, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": false, "mode": "balance", "filters": [], "export_filename": "武汉人大_基层人大.csv", "classid": "3806"}, "贵阳人大_立法聚焦": {"input_url": "https://www.gysrd.gov.cn/lfjw/lfjj/", "base_url": "https://www.gysrd.gov.cn/lfjw/lfjj/", "max_pages": "1", "list_container_selector": ".list_box", "list_container_type": "CSS", "article_item_selector": ".list_box li", "article_item_type": "CSS", "title_selector": ".xw_title", "title_selector_type": "CSS", "content_selectors": ".detailsMain", "content_type": "CSS", "date_selector": ".attribute span:nth-child(3)", "date_selector_type": "CSS", "source_selector": ".attribute span:nth-child(2)", "source_selector_type": "CSS", "page_suffix": "index_{n}.html", "page_suffix_start": 1, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": false, "mode": "balance", "filters": [], "export_filename": "", "classid": "3803", "file_format": "Excel"}, "贵阳人大_监督工作": {"input_url": "http://www.gysrd.gov.cn/jdzx/jddt/", "base_url": "http://www.gysrd.gov.cn/jdzx/jddt/", "max_pages": "0", "list_container_selector": ".list_box", "list_container_type": "CSS", "article_item_selector": ".list_box li", "article_item_type": "CSS", "title_selector": ".xw_title", "title_selector_type": "CSS", "content_selectors": ".detailsMain", "content_type": "CSS", "date_selector": ".attribute span:nth-child(3)", "date_selector_type": "CSS", "source_selector": ".attribute span:nth-child(2)", "source_selector_type": "CSS", "page_suffix": "index_{n}.html", "page_suffix_start": 1, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": false, "mode": "balance", "filters": [], "export_filename": "贵阳人大_监督", "classid": "3803", "file_format": "Excel"}, "贵阳人大_机关建设": {"input_url": "http://www.gysrd.gov.cn/jgjs/jgdj/", "base_url": "http://www.gysrd.gov.cn/jgjs/jgdj/", "max_pages": "0", "list_container_selector": ".list_box", "list_container_type": "CSS", "article_item_selector": ".list_box li", "article_item_type": "CSS", "title_selector": ".xw_title", "title_selector_type": "CSS", "content_selectors": ".detailsMain", "content_type": "CSS", "date_selector": ".attribute span:nth-child(3)", "date_selector_type": "CSS", "source_selector": ".attribute span:nth-child(2)", "source_selector_type": "CSS", "page_suffix": "index_{n}.html", "page_suffix_start": 1, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": false, "mode": "balance", "filters": [], "export_filename": "贵阳人大_机关建设", "classid": "3804", "file_format": "Excel"}, "贵阳人大_理论研究": {"input_url": "http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign=", "base_url": "", "max_pages": "0", "list_container_selector": ".js_basic_result_content", "list_container_type": "CSS", "article_item_selector": ".item.hasImg.is-news", "article_item_type": "CSS", "title_selector": ".xw_title", "title_selector_type": "CSS", "content_selectors": ".detailsMain .TRS_UEDITOR", "content_type": "CSS", "date_selector": ".attribute span:nth-child(3)", "date_selector_type": "CSS", "source_selector": ".attribute span:nth-child(2)", "source_selector_type": "CSS", "page_suffix": "index_{n}.html", "page_suffix_start": 1, "dynamic_pagination_type": "javascript_click", "pagination_config": {"pagination_type": "点击翻页", "enabled": true, "next_button_selector": "a.next:not(.lose)", "content_ready_selector": "", "max_pages": 5, "wait_after_click": 1500, "timeout": 10000, "disabled_check": true}, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "1200,800", "page_load_strategy": "normal", "collect_links": false, "mode": "balance", "filters": [], "export_filename": "贵阳人大_理论研究", "classid": "3807", "file_format": "Excel", "max_workers": 5, "retry": 3, "interval": 0.5}, "成都人大": {"input_url": "http://www.cdrd.gov.cn/website/dbfc/index.jhtml", "base_url": "http://www.cdrd.gov.cn/website/dbfc/", "max_pages": "1", "list_container_selector": ".contentsList", "list_container_type": "CSS", "article_item_selector": ".contentsList p", "article_item_type": "CSS", "title_selector": ".title", "title_selector_type": "CSS", "content_selectors": ".text-field-body", "content_type": "CSS", "date_selector": ".date", "date_selector_type": "CSS", "source_selector": ".text-field-head span:nth-of-type(2)", "source_selector_type": "CSS", "page_suffix": "index_{n}.jhtml", "page_suffix_start": 0, "dynamic_pagination_type": "traditional", "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": false, "mode": "safe", "filters": [], "export_filename": "成都人大_代表工作", "classid": "3805", "file_format": "Excel"}, "成都人大_机关建设": {"input_url": "http://www.cdrd.gov.cn/website/jgjs/index.jhtml", "base_url": "http://www.cdrd.gov.cn/website/jgjs/", "max_pages": "0", "list_container_selector": ".section_01_div", "list_container_type": "CSS", "article_item_selector": ".groupList p", "article_item_type": "CSS", "title_selector": ".title", "title_selector_type": "CSS", "content_selectors": ".text-field-body", "content_type": "CSS", "date_selector": ".date", "date_selector_type": "CSS", "source_selector": ".text-field-head span:nth-of-type(2)", "source_selector_type": "CSS", "page_suffix": "index_{n}.jhtml", "page_suffix_start": 0, "dynamic_pagination_type": null, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": false, "mode": "safe", "filters": [], "export_filename": "成都人大_机关建设", "classid": "3805", "file_format": "Excel"}, "珠海人大": {"input_url": "http://www.zhrd.gov.cn/dbgz/dbya/", "base_url": "http://www.zhrd.gov.cn/dbgz/dbya/", "max_pages": "0", "list_container_selector": ".n_new_list > ul", "list_container_type": "CSS", "article_item_selector": ".n_new_list > ul > li", "article_item_type": "CSS", "title_selector": ".n_new_xx_bt", "title_selector_type": "CSS", "content_selectors": ".TRS_Editor", "content_type": "CSS", "date_selector": ".n_new_xx_bt span", "date_selector_type": "CSS", "source_selector": "", "source_selector_type": "CSS", "page_suffix": "index_{n}.html", "page_suffix_start": 1, "dynamic_pagination_type": null, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": false, "mode": "balance", "filters": [], "export_filename": "珠海人大_议案建议", "classid": "3802", "file_format": "Excel"}, "厦门人大": {"input_url": "https://www.xmrd.gov.cn/rdlz/jdgz/", "base_url": "https://www.xmrd.gov.cn/rdlz/jdgz/", "max_pages": "0", "list_container_selector": ".list.clearfix", "list_container_type": "CSS", "article_item_selector": ".list.clearfix ul li", "article_item_type": "CSS", "title_selector": ".ch h2", "title_selector_type": "CSS", "content_selectors": ".TRS_Editor;.cb", "content_type": "CSS", "date_selector": ".i-2", "date_selector_type": "CSS", "source_selector": ".i-1", "source_selector_type": "CSS", "page_suffix": "index_{n}.htm", "page_suffix_start": 0, "dynamic_pagination_type": null, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "1200,800", "page_load_strategy": "normal", "collect_links": false, "mode": "balance", "filters": [], "export_filename": "厦门人大_监督工作", "classid": "3803", "file_format": "Excel", "max_workers": 10, "retry": 1, "interval": 0.5}, "上海人大_议案建议": {"input_url": "https://www.shrd.gov.cn/n8347/n3802/n9943/index.html", "base_url": "https://www.shrd.gov.cn/n8347/n3802/n9943/", "max_pages": "1", "list_container_selector": "table[width=\"100%\"][bgcolor=\"#b7babf\"]", "list_container_type": "CSS", "article_item_selector": "tr.grey14.lh24", "article_item_type": "CSS", "title_selector": ".blue30", "title_selector_type": "CSS", "content_selectors": ".dbjycnt.gray16", "content_type": "CSS", "date_selector": "", "date_selector_type": "CSS", "source_selector": ".gray14", "source_selector_type": "CSS", "page_suffix": "index{n}.html", "page_suffix_start": 0, "dynamic_pagination_type": null, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "1200,800", "page_load_strategy": "normal", "collect_links": true, "mode": "balance", "filters": [], "export_filename": "上海人大_议案建议", "classid": "3802", "file_format": "Excel", "max_workers": 5, "retry": 3, "interval": 0.5}, "上海人大": {"input_url": "https://www.shrd.gov.cn/n8347/n8378/index.html", "base_url": "", "max_pages": "0", "list_container_selector": ".gdlist", "list_container_type": "CSS", "article_item_selector": ".gdlist li", "article_item_type": "CSS", "title_selector": "h1.rich_media_title", "title_selector_type": "CSS", "content_selectors": ".rich_media_content", "content_type": "CSS", "date_selector": "em.publish_time", "date_selector_type": "CSS", "source_selector": "a.wx_tap_link", "source_selector_type": "CSS", "page_suffix": "index_{n}.html", "page_suffix_start": 1, "dynamic_pagination_type": "infinite_scroll", "pagination_config": {"pagination_type": "滚动翻页", "enabled": true, "scroll_container_selector": "#largeData", "scroll_step": 1000, "scroll_delay": 1000, "max_scrolls": 99, "load_indicator_selector": "", "scroll_timeout": 10000, "height_tolerance": 50, "load_element_pattern": "#load{n}"}, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "1200,800", "page_load_strategy": "normal", "collect_links": false, "mode": "balance", "filters": [], "export_filename": "", "classid": "3802", "file_format": "Excel", "max_workers": 5, "retry": 3, "interval": 0.5}}}
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, ElementNotInteractableException
import time
import re

class CustomPageNavigator:
    def __init__(self, driver, config):
        """
        自定义分页导航器
        
        参数:
        driver: Selenium WebDriver实例
        config: 配置字典，包含以下键值:
            - next_btn_selector: 下一页按钮选择器
            - prev_btn_selector: 上一页按钮选择器
            - current_page_selector: 当前页码元素选择器
            - total_pages_selector: 总页数元素选择器
            - page_input_selector: 页码输入框选择器
            - table_row_selector: 表格行选择器
            - first_row_index: 第一行索引(通常是0)
            - wait_timeout: 等待超时时间(秒)
            - extra_wait: 额外等待时间(秒)
            - page_pattern: 提取页码的正则表达式
        """
        self.driver = driver
        self.config = config
        self.wait = WebDriverWait(driver, config.get('wait_timeout', 15))
    
    def get_current_page(self):
        """获取当前页码"""
        element = self.wait.until(
            EC.presence_of_element_located(
                (By.CSS_SELECTOR, self.config['current_page_selector'])
            )
        )
        return int(element.get_attribute("value"))
    
    def get_total_pages(self):
        """获取总页数"""
        element = self.wait.until(
            EC.presence_of_element_located(
                (By.CSS_SELECTOR, self.config['total_pages_selector'])
            )
        )
        
        # 使用正则表达式提取数字
        pattern = self.config.get('page_pattern', r'(\d+)')
        match = re.search(pattern, element.text)
        if match:
            return int(match.group(1))
        raise ValueError(f"无法从文本 '{element.text}' 中提取页码")
    
    def wait_for_table_refresh(self, old_first_row_text=None):
        """等待表格刷新完成"""
        try:
            # 等待表格行出现
            self.wait.until(
                EC.presence_of_element_located(
                    (By.CSS_SELECTOR, self.config['table_row_selector'])
                )
            )
            
            # 如果有旧数据，等待数据变化
            if old_first_row_text:
                row_index = self.config.get('first_row_index', 0)
                selector = f"{self.config['table_row_selector']}:nth-child({row_index + 1})"
                
                self.wait.until(
                    lambda d: d.find_element(By.CSS_SELECTOR, selector).text != old_first_row_text
                )
            
            # 额外等待以确保完全加载
            time.sleep(self.config.get('extra_wait', 1))
            return True
        except TimeoutException:
            print("表格刷新超时")
            return False
    
    def go_to_next_page(self):
        """翻到下一页"""
        current_page = self.get_current_page()
        total_pages = self.get_total_pages()
        
        if current_page >= total_pages:
            print("已在最后一页")
            return False
        
        # 获取当前第一行文本作为刷新参考
        row_index = self.config.get('first_row_index', 0)
        selector = f"{self.config['table_row_selector']}:nth-child({row_index + 1})"
        first_row = self.driver.find_element(By.CSS_SELECTOR, selector)
        old_text = first_row.text
        
        try:
            next_button = self.wait.until(
                EC.element_to_be_clickable(
                    (By.CSS_SELECTOR, self.config['next_btn_selector'])
                )
            )
            next_button.click()
            
            # 等待页面刷新
            return self.wait_for_table_refresh(old_text)
        except (ElementNotInteractableException, TimeoutException) as e:
            print(f"翻页失败: {str(e)}")
            return False
    
    def go_to_previous_page(self):
        """翻到上一页"""
        current_page = self.get_current_page()
        
        if current_page <= 1:
            print("已在第一页")
            return False
        
        # 获取当前第一行文本作为刷新参考
        row_index = self.config.get('first_row_index', 0)
        selector = f"{self.config['table_row_selector']}:nth-child({row_index + 1})"
        first_row = self.driver.find_element(By.CSS_SELECTOR, selector)
        old_text = first_row.text
        
        try:
            prev_button = self.wait.until(
                EC.element_to_be_clickable(
                    (By.CSS_SELECTOR, self.config['prev_btn_selector'])
                )
            )
            prev_button.click()
            
            # 等待页面刷新
            return self.wait_for_table_refresh(old_text)
        except (ElementNotInteractableException, TimeoutException) as e:
            print(f"翻页失败: {str(e)}")
            return False
    
    def go_to_page(self, page_number):
        """跳转到指定页码"""
        total_pages = self.get_total_pages()
        
        if page_number < 1 or page_number > total_pages:
            print(f"无效页码: {page_number}，有效范围: 1-{total_pages}")
            return False
        
        current_page = self.get_current_page()
        if current_page == page_number:
            print(f"已在第 {page_number} 页")
            return True
        
        # 获取当前第一行文本作为刷新参考
        row_index = self.config.get('first_row_index', 0)
        selector = f"{self.config['table_row_selector']}:nth-child({row_index + 1})"
        first_row = self.driver.find_element(By.CSS_SELECTOR, selector)
        old_text = first_row.text
        
        try:
            page_input = self.wait.until(
                EC.element_to_be_clickable(
                    (By.CSS_SELECTOR, self.config['page_input_selector'])
                )
            )
            
            # 清除当前内容并输入新页码
            page_input.clear()
            page_input.send_keys(str(page_number))
            page_input.send_keys(Keys.RETURN)
            
            # 等待页面刷新
            return self.wait_for_table_refresh(old_text)
        except (ElementNotInteractableException, TimeoutException) as e:
            print(f"跳转失败: {str(e)}")
            return False
    
    def navigate_through_all_pages(self, page_handler):
        """
        遍历所有页面并处理数据
        
        参数:
        page_handler: 页面处理函数，接受当前页码作为参数
        """
        total_pages = self.get_total_pages()
        print(f"开始遍历 {total_pages} 页数据...")
        
        for page in range(1, total_pages + 1):
            # 如果不是第一页，需要翻页
            if page > 1:
                if not self.go_to_page(page):
                    print(f"无法跳转到第 {page} 页，终止遍历")
                    break
            
            print(f"正在处理第 {page}/{total_pages} 页")
            
            # 处理当前页数据
            page_handler(page)
            
            # 添加页面处理后的延迟
            time.sleep(self.config.get('page_delay', 2))
        
        print("所有页面处理完成")
import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QLabel, QLineEdit,
                            QPushButton, QTextEdit, QVBoxLayout, QHBoxLayout,
                            QGroupBox, QGridLayout, QMessageBox, QProgressBar,
                            QComboBox, QInputDialog, QFrame, QTabWidget, QCheckBox, QSpinBox, QDoubleSpinBox)  # 添加更多控件
from PyQt5.QtCore import Qt, QThread, pyqtSignal
import crawler
import myconfig
import PaginationHandler

class CrawlerThread(QThread):
    # 定义信号
    log_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(dict)
    progress_signal = pyqtSignal(int, int)  # 当前进度, 总数
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
    def run(self):
        # 将日志信号传递给爬虫
        self.config['log_callback'] = self.log_signal.emit

        # 检查是否使用动态翻页
        pagination_config = self.config.get('pagination_config', {})
        dynamic_pagination_type = self.config.get('dynamic_pagination_type')

        # 如果启用了动态翻页且不是传统翻页，使用PaginationHandler
        if (pagination_config.get('enabled', False) and
            pagination_config.get('pagination_type') != '禁用动态翻页') or \
           (dynamic_pagination_type and dynamic_pagination_type != 'traditional'):

            self.log_signal.emit("使用动态翻页模式，调用PaginationHandler...")
            result = self.run_dynamic_pagination()
        else:
            # 使用传统翻页
            self.log_signal.emit("使用传统翻页模式...")
            result = crawler.crawl_articles(**self.config)

        self.finished_signal.emit(result)

    def run_dynamic_pagination(self):
        """运行动态翻页爬取"""
        import asyncio
        from playwright.async_api import async_playwright

        try:
            # 运行异步动态翻页
            return asyncio.run(self._async_dynamic_pagination())
        except Exception as e:
            self.log_signal.emit(f"动态翻页处理失败: {e}")
            # 回退到传统翻页
            self.log_signal.emit("回退到传统翻页模式...")
            return crawler.crawl_articles(**self.config)

    async def _async_dynamic_pagination(self):
        """异步动态翻页处理"""
        from playwright.async_api import async_playwright  # 添加Playwright导入

        pagination_config = self.config.get('pagination_config', {})

        # 获取配置参数
        input_url = self.config.get('input_url')
        max_pages = self.config.get('max_pages', 5)
        list_container_selector = self.config.get('list_container_selector', '.main')
        article_item_selector = self.config.get('article_item_selector', '.clearfix.ty_list li a')
        title_selector = self.config.get('title_selector')

        # 翻页配置
        pagination_type = pagination_config.get('pagination_type', 'JavaScript点击')
        next_button_selector = pagination_config.get('next_button_selector', 'a.next:not(.lose)')

        async with async_playwright() as p:  # 现在可以正确识别async_playwright
            # 启动浏览器
            browser, context, page = await PaginationHandler.launch_browser(p, headless=False)

            try:
                # 创建PaginationHandler实例
                handler = PaginationHandler.PaginationHandler(page)

                # 访问起始页面
                await page.goto(input_url)
                self.log_signal.emit(f"访问起始页面: {input_url}")

                # 根据翻页类型执行相应的翻页操作
                if pagination_type == 'JavaScript点击':
                    # 准备文章提取配置
                    extract_config = {
                        'list_container_selector': list_container_selector,
                        'article_item_selector': article_item_selector,
                        'title_selector': title_selector,
                        'save_dir': "动态翻页结果",
                        'page_title': "动态翻页结果",
                        'classid': self.config.get('classid', ''),
                        'base_url': self.config.get('base_url'),
                        'url_mode': self.config.get('url_mode', 'relative')
                    }

                    pages_processed = await handler.click_pagination(
                        next_button_selector=next_button_selector,
                        max_pages=max_pages,
                        content_ready_selector=pagination_config.get('content_ready_selector'),
                        timeout=pagination_config.get('timeout', 10000),
                        wait_after_click=pagination_config.get('wait_after_click', 2000),
                        disabled_check=pagination_config.get('disabled_check', True),
                        extract_articles_config=extract_config
                    )
                elif pagination_type == '无限滚动':
                    pages_processed = await handler.scroll_pagination(
                        max_scrolls=max_pages,
                        scroll_delay=pagination_config.get('scroll_delay', 2000),
                        extract_callback=lambda: handler.extract_articles_from_page(
                            list_container_selector=list_container_selector,
                            article_item_selector=article_item_selector,
                            title_selector=title_selector,
                            save_dir="动态翻页结果",
                            page_title="动态翻页结果",
                            classid=self.config.get('classid', '')
                        )
                    )
                else:
                    self.log_signal.emit(f"暂不支持的翻页类型: {pagination_type}")
                    return {"total": 0, "success": 0, "failed": 0}

                # 获取所有收集到的文章链接
                all_articles = handler.get_all_articles()
                self.log_signal.emit(f"动态翻页完成，共收集到 {len(all_articles)} 篇文章链接")

                # 动态翻页模块只负责收集href，传递给爬虫模块下载
                if all_articles:
                    self.log_signal.emit("开始将收集的文章链接传递给爬虫模块进行下载...")

                    # 准备传递给爬虫模块的配置
                    config_copy = self.config.copy()

                    # 移除动态翻页相关参数，使用传统爬虫模式进行下载
                    config_copy.pop('pagination_config', None)
                    config_copy['dynamic_pagination_type'] = 'traditional'
                    config_copy['all_articles'] = all_articles  # 传递收集到的文章列表

                    # 调用爬虫模块进行文章下载
                    self.log_signal.emit("调用爬虫模块开始下载文章内容...")
                    result = crawler.crawl_articles(**config_copy)

                    self.log_signal.emit("文章下载完成！")
                    return result
                else:
                    self.log_signal.emit("未收集到任何文章链接")
                    return {"total": 0, "success": 0, "failed": 0}

            except Exception as e:  # 将except块移到finally之前
                self.log_signal.emit(f"动态翻页异步处理出错: {e}")
                return {"total": 0, "success": 0, "failed": 0}
            finally:  # 确保浏览器关闭
                await browser.close()

class AIAutoConfigHelper:
    """AI智能配置工具类，负责AI分析和详情页URL提取"""
    @staticmethod
    def _parse_ai_result(ai_result: str) -> dict:
        """通用解析AI返回的键值对结果"""
        result_dict = {}
        for line in ai_result.splitlines():
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                # 跳过空值结果
                if value.lower() not in ('none', 'null', ''):
                    result_dict[key] = value
        return result_dict

    @staticmethod
    def analyze_list_page(url: str) -> dict:
        from AI_wed_find_agent import analyze_page_with_selenium, analyze_container_with_deepseek_list
        try:
            page_data = analyze_page_with_selenium(url)
            ai_result = analyze_container_with_deepseek_list(page_data)
            return AIAutoConfigHelper._parse_ai_result(ai_result)
        except Exception as e:
            # 返回基础选择器作为降级方案
            return {
                'list_container_selector': 'body',
                'article_item_selector': 'a[href*="article"]'
            }

    @staticmethod
    def extract_first_article_url(
        list_url: str,
        article_item_selector: str,
        list_container_selector: str = "body",
        list_container_type: str = "CSS",
        article_item_type: str = "CSS",
        url_mode: str = "absolute",
        base_url: str = None
    ) -> str:
        import crawler
        import selenium_diver_change
        
        driver = None
        try:
            driver = selenium_diver_change.get_driver(browser="firefox", diver={"headless": True})
            _, _, article_links, _ = crawler.get_article_links(
                driver, list_url,
                list_container_selector or "body",  # 确保选择器不为空
                article_item_selector,
                list_container_type,
                article_item_type
            )
            
            for href in article_links[:3]:  # 最多尝试前3个链接
                full_url = crawler.get_full_link(
                    href, 
                    list_url, 
                    base_url or list_url, 
                    url_mode
                )
                if full_url:
                    return full_url
        except Exception:
            pass
        finally:
            if driver:
                driver.quit()
        return None

    @staticmethod
    def analyze_detail_page(article_url: str) -> dict:
        from AI_wed_find_agent import analyze_page_with_selenium, analyze_container_with_deepseek_words
        try:
            detail_data = analyze_page_with_selenium(article_url)
            detail_result = analyze_container_with_deepseek_words(detail_data)
            return AIAutoConfigHelper._parse_ai_result(detail_result)
        except Exception:
            return {}  # 返回空字典避免上层崩溃

class AIConfigThread(QThread):
    result_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)

    def __init__(self, url: str):
        super().__init__()
        self.url = url

    def run(self):
        try:
            # 1. 分析列表页 (带降级方案)
            result_dict = AIAutoConfigHelper.analyze_list_page(self.url)
            
            # 2. 提取详情页URL (增加选择器存在性检查)
            article_selector = result_dict.get('article_item_selector', '')
            if not article_selector:
                raise ValueError("未获取到文章项选择器")
                
            article_url = AIAutoConfigHelper.extract_first_article_url(
                self.url, 
                article_selector,
                result_dict.get('list_container_selector')  # 传递可选容器选择器
            )
            
            if article_url:
                detail_dict = AIAutoConfigHelper.analyze_detail_page(article_url)
                # 智能合并结果：仅补充缺失的关键字段
                for key in ['title_selector', 'content_selectors', 'date_selector', 'source_selector']:
                    if detail_dict.get(key) and not result_dict.get(key):
                        result_dict[key] = detail_dict[key]
                result_dict['__article_url'] = article_url
                
            self.result_signal.emit(result_dict)
            
        except Exception as e:
            self.error_signal.emit(f"AI配置失败: {str(e)}")

class CrawlerGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("文章智能采集器 v1.0")
        self.setGeometry(100, 100, 1000, 700)  # 加大窗口尺寸

        # 设置应用样式
        self.setStyleSheet(self.get_stylesheet())
        
        # 初始化配置管理器
        self.config_manager = myconfig.ConfigManager()

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 主布局：左右分栏
        self.main_layout = QHBoxLayout()
        self.main_layout.setSpacing(15)
        self.main_layout.setContentsMargins(15, 15, 15, 15)
        self.central_widget.setLayout(self.main_layout)

        # 左侧：标签页配置区
        self.left_widget = QWidget()
        self.left_widget.setObjectName("leftPanel")
        self.left_layout = QVBoxLayout(self.left_widget)
        self.left_layout.setSpacing(10)
        self.left_layout.setContentsMargins(10, 10, 10, 10)

        # 右侧：控制和日志区
        self.right_widget = QWidget()
        self.right_widget.setObjectName("rightPanel")
        self.right_layout = QVBoxLayout(self.right_widget)
        self.right_layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题区域
        title_layout = QHBoxLayout()
        self.title_label = QLabel("Hillsun's文章智能采集器")
        self.title_label.setObjectName("appTitle")
        title_layout.addWidget(self.title_label)
        title_layout.addStretch()

        self.version_label = QLabel("v1.0")
        self.version_label.setObjectName("versionLabel")
        title_layout.addWidget(self.version_label)
        self.left_layout.addLayout(title_layout)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("separator")
        self.left_layout.addWidget(separator)

        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setObjectName("configTabs")

        # 基础配置标签页
        self.basic_tab = QWidget()
        self.basic_layout = QVBoxLayout(self.basic_tab)
        self.basic_layout.setSpacing(10)
        self.basic_layout.setContentsMargins(10, 10, 10, 10)

        # 高级设置标签页
        self.advanced_tab = QWidget()
        self.advanced_layout = QVBoxLayout(self.advanced_tab)
        self.advanced_layout.setSpacing(10)
        self.advanced_layout.setContentsMargins(10, 10, 10, 10)

        # 动态翻页设置标签页
        self.pagination_tab = QWidget()
        self.pagination_layout = QVBoxLayout(self.pagination_tab)
        self.pagination_layout.setSpacing(10)
        self.pagination_layout.setContentsMargins(10, 10, 10, 10)

        # 添加标签页
        self.tab_widget.addTab(self.basic_tab, "基础配置")
        self.tab_widget.addTab(self.advanced_tab, "高级设置")
        self.tab_widget.addTab(self.pagination_tab, "动态翻页")

        self.left_layout.addWidget(self.tab_widget)

        # 在基础配置页创建控件
        self.create_config_group_in_tab()
        self.create_url_group_in_tab()
        self.create_selector_group_in_tab()

        # 在高级设置页创建控件
        self.create_advanced_settings()

        # 在动态翻页页创建控件
        self.create_pagination_settings()

        # 右侧创建控制区和日志区
        self.create_control_area()  # 配置组选择和操作按钮
        self.create_log_area()  # 日志区域
        self.create_progress_bar()  # 进度条

        # 作者标签
        self.author_label = QLabel('© 2025 Hillsun | <EMAIL>')
        self.author_label.setAlignment(Qt.AlignCenter)
        self.author_label.setObjectName("authorLabel")
        self.left_layout.addWidget(self.author_label)

        # 左右布局加入主布局
        self.main_layout.addWidget(self.left_widget, 2)
        self.main_layout.addWidget(self.right_widget, 3)

        # 加载配置
        self.load_config()
        self.crawler_thread = None
        self.ai_thread = None  # 初始化AI线程

    def get_stylesheet(self):
        """返回应用样式表"""
        return """
            QComboBox {
                border-radius: 6px;
                padding: 5px;
                min-width: 100px;
            }
            
            QComboBox:hover {
                border: 1px solid #3498db;
                background-color: #f8fbfd;
            }
            
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left-width: 1px;
                border-left-color: #dce0e3;
                border-left-style: solid;
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
            }
            
            QComboBox QAbstractItemView {
                border: 1px solid #dce0e3;
                border-radius: 6px;
                background-color: white;
                selection-background-color: #3498db;
                selection-color: white;
                outline: none;
                padding: 5px;
                margin: 0;
            }
            
            QComboBox QAbstractItemView::item {
                padding: 5px 10px;
                min-height: 25px;
            }
            
            QComboBox QAbstractItemView::item:hover {
                background-color: #e3f2fd;
            }
            QMainWindow {
                background-color: #f5f7fa;
                border-radius: 12px;
            }
            
            QWidget {
                background-color: #f5f7fa;
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            }
            
            #leftPanel, #rightPanel {
                background-color: white;
                border-radius: 12px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            }
            
            #appTitle {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
            }
            
            #versionLabel {
                font-size: 14px;
                color: #7f8c8d;
                padding: 4px 8px;
                background-color: #ecf0f1;
                border-radius: 4px;
            }
            
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                border: 1px solid #dce0e3;
                border-radius: 8px;  /* 增大圆角半径 */
                margin-top: 1.5ex;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
            }
            
            QLabel {
                color: #34495e;
                font-size: 13px;
            }
            
            QLineEdit, QComboBox, QTextEdit {
                background-color: #ffffff;
                border: 1px solid #dce0e3;
                border-radius: 4px;
                padding: 6px;
                font-size: 13px;
                color: #2c3e50;
            }
            
            QLineEdit:focus, QComboBox:focus {
                border: 1px solid #3498db;
                background-color: #f8fbfd;
            }
            
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;  /* 增大按钮圆角 */
                padding: 8px 16px;
                font-weight: 600;
                font-size: 13px;
            }
            
            QPushButton:hover {
                background-color: #2980b9;
            }
            
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
            
            #runButton {
                background-color: #2ecc71;
            }
            
            #runButton:hover {
                background-color: #27ae60;
            }
            
            #runButton:pressed {
                background-color: #219653;
            }
            
            #stopButton {
                background-color: #e74c3c;
            }
            
            #stopButton:hover {
                background-color: #c0392b;
            }
            
            #stopButton:pressed {
                background-color: #a23526;
            }
            
            #aiButton {
                background-color: #f39c12;
            }
            
            #aiButton:hover {
                background-color: #d35400;
            }
            
            #aiButton:pressed {
                background-color: #ba4a00;
            }
            
            #clearButton {
                background-color: #95a5a6;
            }
            
            #clearButton:hover {
                background-color: #7f8c8d;
            }
            
            #clearButton:pressed {
                background-color: #6c7a7d;
            }
            
            QProgressBar {
                border: 1px solid #dce0e3;
                border-radius: 4px;
                text-align: center;
                background-color: #ecf0f1;
            }
            
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
            
            #logArea {
                background-color: #ffffff;
                border: 1px solid #dce0e3;
                border-radius: 4px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12px;
            }
            
            #authorLabel {
                color: #7f8c8d;
                font-size: 12px;
                padding-top: 10px;
            }
            
            #separator {
                color: #dce0e3;
                background-color: #dce0e3;
                height: 1px;
            }
            QComboBox::down-arrow {
                image: url(:/icons/down_arrow);
                width: 12px;
                height: 12px;
            }
            QComboBox::down-arrow:pressed {
                border-top-color: #3498db;
            }
             #linkButton {
                background-color: #2ecc71;
            }
            #linkButton:checked {
                background-color: #e74c3c;
            }
            #linkButton:hover {
                opacity: 0.9;
            }
            #linkButton:pressed {
                opacity: 0.8;
            }
        """

    def create_config_group_in_tab(self):
        """在基础配置标签页中创建配置组管理区域"""
        group = QGroupBox("配置管理")
        layout = QHBoxLayout()
        layout.setSpacing(8)

        # 配置组选择
        self.config_combo = QComboBox()
        self.config_combo.setMinimumWidth(150)
        self.config_combo.currentTextChanged.connect(self.on_config_changed)
        layout.addWidget(QLabel("配置组:"))
        layout.addWidget(self.config_combo)

        # 新建配置按钮
        self.new_config_button = QPushButton("新建")
        self.new_config_button.setObjectName("actionButton")
        self.new_config_button.clicked.connect(self.create_new_config)
        layout.addWidget(self.new_config_button)

        # 删除配置按钮
        self.delete_config_button = QPushButton("删除")
        self.delete_config_button.setObjectName("deleteButton")
        self.delete_config_button.clicked.connect(self.delete_config)
        layout.addWidget(self.delete_config_button)

        # AI智能配置按钮
        self.ai_button = QPushButton("AI智能配置")
        self.ai_button.setObjectName("aiButton")
        self.ai_button.clicked.connect(self.start_ai_config)
        layout.addWidget(self.ai_button)

        group.setLayout(layout)
        self.basic_layout.addWidget(group)

    # 旧的create_config_group方法已被create_config_group_in_tab替代
    
    def create_url_group_in_tab(self):
        """在基础配置标签页中创建URL设置组"""
        group = QGroupBox("URL 设置")
        grid = QGridLayout()
        grid.setVerticalSpacing(8)
        grid.setHorizontalSpacing(10)

        # 起始URL
        grid.addWidget(QLabel("起始URL:"), 0, 0)
        self.input_url_edit = QLineEdit()
        self.input_url_edit.setPlaceholderText("请输入要爬取的起始页面URL")
        grid.addWidget(self.input_url_edit, 0, 1, 1, 2)

        # 基础URL
        grid.addWidget(QLabel("基础URL:"), 1, 0)
        self.base_url_edit = QLineEdit()
        self.base_url_edit.setPlaceholderText("用于拼接相对链接的基础URL")
        grid.addWidget(self.base_url_edit, 1, 1, 1, 2)

        # 最大页数
        grid.addWidget(QLabel("最大页数:"), 2, 0)
        self.max_pages_edit = QLineEdit()
        self.max_pages_edit.setPlaceholderText("0表示不限制")
        grid.addWidget(self.max_pages_edit, 2, 1)

        # 翻页后缀
        grid.addWidget(QLabel("翻页后缀:"), 3, 0)
        self.page_suffix_edit = QLineEdit()
        self.page_suffix_edit.setPlaceholderText("例如: index_{n}.html")
        grid.addWidget(self.page_suffix_edit, 3, 1)

        # 翻页起始数字
        grid.addWidget(QLabel("起始数字:"), 3, 2)
        self.page_suffix_start_edit = QLineEdit()
        self.page_suffix_start_edit.setPlaceholderText("1")
        self.page_suffix_start_edit.setMaximumWidth(60)
        grid.addWidget(self.page_suffix_start_edit, 3, 3)

        # 动态翻页设置
        grid.addWidget(QLabel("动态翻页:"), 4, 0)
        self.dynamic_pagination_combo = QComboBox()
        self.dynamic_pagination_combo.addItems(["禁用", "自动检测", "JavaScript点击", "无限滚动", "下拉框选择"])
        grid.addWidget(self.dynamic_pagination_combo, 4, 1)

        # 翻页间隔
        grid.addWidget(QLabel("翻页间隔(秒):"), 5, 0)
        self.pagination_delay_edit = QLineEdit()
        self.pagination_delay_edit.setPlaceholderText("默认2秒")
        self.pagination_delay_edit.setText("2")
        grid.addWidget(self.pagination_delay_edit, 5, 1)

        group.setLayout(grid)
        self.basic_layout.addWidget(group)

    def create_url_group(self):
        group = QGroupBox("URL 设置")
        grid = QGridLayout()
        grid.setVerticalSpacing(8)
        grid.setHorizontalSpacing(10)
        
        # 输入URL
        self.input_url_label = QLabel("输入URL:")
        self.input_url_edit = QLineEdit()
        self.input_url_edit.setPlaceholderText("请输入要采集的网址...")
        grid.addWidget(self.input_url_label, 0, 0)
        grid.addWidget(self.input_url_edit, 0, 1, 1, 3)
        
        # 基础URL
        self.base_url_label = QLabel("基础URL:")
        self.base_url_edit = QLineEdit()
        self.base_url_edit.setPlaceholderText("网站基础URL...")
        grid.addWidget(self.base_url_label, 1, 0)
        grid.addWidget(self.base_url_edit, 1, 1, 1, 3)
        
        # 最大页数
        self.max_pages_label = QLabel("最大页数:")
        self.max_pages_edit = QLineEdit()
        self.max_pages_edit.setPlaceholderText("3")
        self.max_pages_edit.setMaximumWidth(80)
        grid.addWidget(self.max_pages_label, 2, 0)
        grid.addWidget(self.max_pages_edit, 2, 1)
        
        # 翻页后缀
        self.page_suffix_label = QLabel("翻页后缀:")
        self.page_suffix_edit = QLineEdit()
        self.page_suffix_edit.setPlaceholderText("index_{n}.html")
        self.page_suffix_edit.setMaximumWidth(150)
        grid.addWidget(self.page_suffix_label, 2, 2)
        grid.addWidget(self.page_suffix_edit, 2, 3)
        
        # 翻页起始数字
        self.page_suffix_start_label = QLabel("起始数字:")
        self.page_suffix_start_edit = QLineEdit()
        self.page_suffix_start_edit.setPlaceholderText("1")
        self.page_suffix_start_edit.setMaximumWidth(60)
        grid.addWidget(self.page_suffix_start_label, 3, 0)
        grid.addWidget(self.page_suffix_start_edit, 3, 1)
        # 动态翻页类型
        self.dynamic_pagination_label = QLabel("动态翻页:")
        self.dynamic_pagination_combo = QComboBox()
        self.dynamic_pagination_combo.addItems([
            "自动检测",
            "传统翻页",
            "JavaScript点击",
            "无限滚动",
            "下拉选择"
        ])
        self.dynamic_pagination_combo.setToolTip(
            "自动检测: 系统自动识别翻页类型\n"
            "传统翻页: 使用URL模式翻页\n"
            "JavaScript点击: 点击按钮翻页\n"
            "无限滚动: 滚动加载更多内容\n"
            "下拉选择: 下拉框选择页面"
        )
        grid.addWidget(self.dynamic_pagination_label, 3, 2)
        grid.addWidget(self.dynamic_pagination_combo, 3, 3)

        # 正文URL规则
        self.url_mode_label = QLabel("URL规则:")
        self.url_mode_combo = QComboBox()
        self.url_mode_combo.addItems(["绝对路径", "相对路径"])
        grid.addWidget(self.url_mode_label, 4, 0)
        grid.addWidget(self.url_mode_combo, 4, 1)
        
        # 浏览器选择
        self.browser_label = QLabel("浏览器:")
        self.browser_combo = QComboBox()
        self.browser_combo.addItems(["Firefox", "Chrome", "Edge"])
        grid.addWidget(self.browser_label, 4, 2)
        grid.addWidget(self.browser_combo, 4, 3)

        # 无头模式选择
        self.headless_label = QLabel("无头模式:")
        self.headless_combo = QComboBox()
        self.headless_combo.addItems(["是", "否"])
        grid.addWidget(self.headless_label, 5, 0)
        grid.addWidget(self.headless_combo, 5, 1)

        # 窗口尺寸
        self.window_size_label = QLabel("窗口尺寸:")
        self.window_size_edit = QLineEdit()
        self.window_size_edit.setPlaceholderText("1200,800")
        grid.addWidget(self.window_size_label, 5, 2)
        grid.addWidget(self.window_size_edit, 5, 3)

        # 页面加载策略
        self.page_load_strategy_label = QLabel("加载策略:")
        self.page_load_strategy_combo = QComboBox()
        self.page_load_strategy_combo.addItems(["normal", "eager", "none"])
        grid.addWidget(self.page_load_strategy_label, 6, 0)
        grid.addWidget(self.page_load_strategy_combo, 6, 1)
        
        # 采集图片与附件链接复选框
        self.collect_links_checkbox = QPushButton("采集图片与附件: 开启")
        self.collect_links_checkbox.setCheckable(True)
        self.collect_links_checkbox.setChecked(True)
        self.collect_links_checkbox.setObjectName("linkButton")
        self.collect_links_checkbox.toggled.connect(self.update_link_button_text)
        grid.addWidget(self.collect_links_checkbox, 6, 2, 1, 2)
        # 新增：采集模式选择
        self.mode_label = QLabel("采集模式:")
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["平衡模式", "快速模式", "安全模式"])
        self.mode_combo.setCurrentIndex(0)
        grid.addWidget(self.mode_label, 7, 0)
        grid.addWidget(self.mode_combo, 7, 1)
        # 新增：内容过滤规则按钮
        self.filter_button = QPushButton("内容过滤规则")
        self.filter_button.setToolTip("设置需过滤的关键词或正则(一行一个)")
        self.filter_button.clicked.connect(self.set_filter_rules)
        grid.addWidget(self.filter_button, 7, 2, 1, 2)
        # 新增：导出文件名输入框
        self.export_filename_label = QLabel("导出文件名:")
        self.export_filename_edit = QLineEdit()
        self.export_filename_edit.setPlaceholderText("可选，如 test")
        grid.addWidget(self.export_filename_label, 8, 0)
        grid.addWidget(self.export_filename_edit, 8, 1, 1, 2)

        # 新增：文件格式选择
        self.file_format_label = QLabel("文件格式:")
        self.file_format_combo = QComboBox()
        self.file_format_combo.addItems(["CSV", "Excel"])
        self.file_format_combo.setCurrentIndex(0)  # 默认选择CSV
        self.file_format_combo.currentTextChanged.connect(self.update_filename_placeholder)
        grid.addWidget(self.file_format_label, 8, 2)
        grid.addWidget(self.file_format_combo, 8, 3)
        # 新增：classid输入框
        self.classid_label = QLabel("classid:")
        self.classid_edit = QLineEdit()
        self.classid_edit.setPlaceholderText("可选，分类标识")
        grid.addWidget(self.classid_label, 9, 0)
        grid.addWidget(self.classid_edit, 9, 1, 1, 3)
        # 新增：并发线程数
        self.max_workers_label = QLabel("并发线程数:")
        self.max_workers_edit = QLineEdit()
        self.max_workers_edit.setPlaceholderText("默认5")
        grid.addWidget(self.max_workers_label, 10, 0)
        grid.addWidget(self.max_workers_edit, 10, 1)
        # 新增：重试次数
        self.retry_label = QLabel("重试次数:")
        self.retry_edit = QLineEdit()
        self.retry_edit.setPlaceholderText("默认3")
        grid.addWidget(self.retry_label, 10, 2)
        grid.addWidget(self.retry_edit, 10, 3)
        # 新增：下载间隔
        self.interval_label = QLabel("下载间隔(秒):")
        self.interval_edit = QLineEdit()
        self.interval_edit.setPlaceholderText("默认0.5")
        grid.addWidget(self.interval_label, 11, 0)
        grid.addWidget(self.interval_edit, 11, 1)
        group.setLayout(grid)
        self.left_layout.addWidget(group)
        self.filters = []  # 存储过滤规则

    def set_filter_rules(self):
        from PyQt5.QtWidgets import QInputDialog
        text, ok = QInputDialog.getMultiLineText(self, "内容过滤规则", "每行一个关键词或正则(匹配到的行将被过滤):", '\n'.join(self.filters) if self.filters else "")
        if ok:
            self.filters = [line.strip() for line in text.splitlines() if line.strip()]

    def on_pagination_type_changed(self):
        """翻页类型改变时的处理"""
        pagination_type = self.pagination_type_combo.currentText()

        # 隐藏所有设置组
        self.click_pagination_group.setVisible(False)
        self.scroll_pagination_group.setVisible(False)
        self.iframe_pagination_group.setVisible(False)

        # 根据选择显示对应的设置组
        if pagination_type == "点击翻页":
            self.click_pagination_group.setVisible(True)
        elif pagination_type == "滚动翻页":
            self.scroll_pagination_group.setVisible(True)
        elif pagination_type == "iframe翻页":
            self.iframe_pagination_group.setVisible(True)

    def test_pagination_settings(self):
        """测试翻页设置"""
        pagination_type = self.pagination_type_combo.currentText()

        if pagination_type == "禁用动态翻页":
            QMessageBox.information(self, "提示", "当前选择的是禁用动态翻页，无需测试")
            return

        # 获取列表页URL
        list_url = self.list_url_edit.text().strip()
        if not list_url:
            QMessageBox.warning(self, "警告", "请先填写列表页URL")
            return

        # 显示测试对话框
        msg = QMessageBox()
        msg.setWindowTitle("翻页测试")
        msg.setText(f"正在测试 {pagination_type} 设置...")
        msg.setStandardButtons(QMessageBox.Ok)
        msg.setIcon(QMessageBox.Information)

        # 这里可以添加实际的测试逻辑
        # 暂时显示配置信息
        config_info = self.get_pagination_config()
        detail_text = f"翻页类型: {pagination_type}\n"
        detail_text += f"列表页URL: {list_url}\n"

        if pagination_type == "点击翻页":
            detail_text += f"下一页按钮选择器: {config_info.get('next_button_selector', '')}\n"
            detail_text += f"最大翻页数: {config_info.get('max_pages', '')}\n"
        elif pagination_type == "滚动翻页":
            detail_text += f"滚动容器选择器: {config_info.get('scroll_container_selector', '')}\n"
            detail_text += f"最大滚动次数: {config_info.get('max_scrolls', '')}\n"
        elif pagination_type == "iframe翻页":
            detail_text += f"iframe选择器: {config_info.get('iframe_selector', '')}\n"

        msg.setDetailedText(detail_text)
        msg.exec_()

    def get_pagination_config(self):
        """获取翻页配置"""
        pagination_type = self.pagination_type_combo.currentText()

        config = {
            'pagination_type': pagination_type,
            'enabled': pagination_type != "禁用动态翻页"
        }

        if pagination_type == "点击翻页":
            config.update({
                'next_button_selector': self.next_button_selector_edit.text().strip(),
                'content_ready_selector': self.content_ready_selector_edit.text().strip(),
                'max_pages': self.max_pages_spin.value(),
                'wait_after_click': self.wait_after_click_spin.value(),
                'timeout': self.click_timeout_spin.value(),
                'disabled_check': self.disabled_check_checkbox.isChecked()
            })
        elif pagination_type == "滚动翻页":
            config.update({
                'scroll_container_selector': self.scroll_container_selector_edit.text().strip(),
                'scroll_step': self.scroll_step_spin.value(),
                'scroll_delay': self.scroll_delay_spin.value(),
                'max_scrolls': self.max_scrolls_spin.value(),
                'load_indicator_selector': self.load_indicator_selector_edit.text().strip(),
                'scroll_timeout': self.scroll_timeout_spin.value(),
                'height_tolerance': self.height_tolerance_spin.value()
            })
        elif pagination_type == "iframe翻页":
            config.update({
                'iframe_selector': self.iframe_selector_edit.text().strip(),
                'iframe_pagination_type': self.iframe_pagination_type_combo.currentText()
            })

        return config

    def create_selector_group_in_tab(self):
        """在基础配置标签页中创建选择器设置组"""
        group = QGroupBox("选择器设置")
        grid = QGridLayout()
        grid.setVerticalSpacing(8)
        grid.setHorizontalSpacing(10)

        # 列表容器选择器
        grid.addWidget(QLabel("列表容器:"), 0, 0)
        self.list_container_edit = QLineEdit()
        self.list_container_edit.setPlaceholderText("包含文章列表的容器选择器")
        grid.addWidget(self.list_container_edit, 0, 1)
        self.list_container_type = QComboBox()
        self.list_container_type.addItems(["CSS", "XPath"])
        grid.addWidget(self.list_container_type, 0, 2)

        # 文章项选择器
        grid.addWidget(QLabel("文章项:"), 1, 0)
        self.article_item_edit = QLineEdit()
        self.article_item_edit.setPlaceholderText("单个文章项的选择器")
        grid.addWidget(self.article_item_edit, 1, 1)
        self.article_item_type = QComboBox()
        self.article_item_type.addItems(["CSS", "XPath"])
        grid.addWidget(self.article_item_type, 1, 2)

        # 标题选择器
        grid.addWidget(QLabel("标题选择器:"), 2, 0)
        self.title_selector_edit = QLineEdit()
        self.title_selector_edit.setPlaceholderText("文章标题选择器(可选)")
        grid.addWidget(self.title_selector_edit, 2, 1)
        self.title_selector_type = QComboBox()
        self.title_selector_type.addItems(["CSS", "XPath"])
        grid.addWidget(self.title_selector_type, 2, 2)

        # 内容选择器
        grid.addWidget(QLabel("内容选择器:"), 3, 0)
        self.content_selector_edit = QLineEdit()
        self.content_selector_edit.setPlaceholderText("文章内容选择器，多个用逗号分隔")
        grid.addWidget(self.content_selector_edit, 3, 1)
        self.content_selector_type = QComboBox()
        self.content_selector_type.addItems(["CSS", "XPath"])
        grid.addWidget(self.content_selector_type, 3, 2)

        # 日期选择器
        grid.addWidget(QLabel("日期选择器:"), 4, 0)
        self.date_selector_edit = QLineEdit()
        self.date_selector_edit.setPlaceholderText("文章日期选择器(可选)")
        grid.addWidget(self.date_selector_edit, 4, 1)
        self.date_selector_type = QComboBox()
        self.date_selector_type.addItems(["CSS", "XPath"])
        grid.addWidget(self.date_selector_type, 4, 2)

        # 来源选择器
        grid.addWidget(QLabel("来源选择器:"), 5, 0)
        self.source_selector_edit = QLineEdit()
        self.source_selector_edit.setPlaceholderText("文章来源选择器(可选)")
        grid.addWidget(self.source_selector_edit, 5, 1)
        self.source_selector_type = QComboBox()
        self.source_selector_type.addItems(["CSS", "XPath"])
        grid.addWidget(self.source_selector_type, 5, 2)

        group.setLayout(grid)
        self.basic_layout.addWidget(group)

    def create_selector_group(self):
        group = QGroupBox("选择器设置")
        grid = QGridLayout()
        grid.setVerticalSpacing(8)
        grid.setHorizontalSpacing(10)
        
        # 列表容器选择器
        self.list_container_label = QLabel("列表容器:")
        self.list_container_edit = QLineEdit()
        self.list_container_type = QComboBox()
        self.list_container_type.addItems(["CSS", "XPath"])
        self.list_container_type.setMaximumWidth(80)
        grid.addWidget(self.list_container_label, 0, 0)
        grid.addWidget(self.list_container_edit, 0, 1)
        grid.addWidget(self.list_container_type, 0, 2)
        
        # 文章项选择器
        self.article_item_label = QLabel("文章项:")
        self.article_item_edit = QLineEdit()
        self.article_item_type = QComboBox()
        self.article_item_type.addItems(["CSS", "XPath"])
        self.article_item_type.setMaximumWidth(80)
        grid.addWidget(self.article_item_label, 1, 0)
        grid.addWidget(self.article_item_edit, 1, 1)
        grid.addWidget(self.article_item_type, 1, 2)
        
        # 文章标题选择器
        self.title_selector_label = QLabel("文章标题:")
        self.title_selector_edit = QLineEdit()
        self.title_selector_type = QComboBox()
        self.title_selector_type.addItems(["CSS", "XPath"])
        self.title_selector_type.setMaximumWidth(80)
        grid.addWidget(self.title_selector_label, 2, 0)
        grid.addWidget(self.title_selector_edit, 2, 1)
        grid.addWidget(self.title_selector_type, 2, 2)
        
        # 正文内容选择器
        self.content_label = QLabel("正文内容:")
        self.content_selector_edit = QLineEdit()
        self.content_selector_edit.setPlaceholderText("多个用;分隔")
        self.content_selector_type = QComboBox()
        self.content_selector_type.addItems(["CSS", "XPath"])
        self.content_selector_type.setMaximumWidth(80)
        grid.addWidget(self.content_label, 3, 0)
        grid.addWidget(self.content_selector_edit, 3, 1)
        grid.addWidget(self.content_selector_type, 3, 2)
        
        # 日期选择器
        self.date_selector_label = QLabel("日期:")
        self.date_selector_edit = QLineEdit()
        self.date_selector_type = QComboBox()
        self.date_selector_type.addItems(["CSS", "XPath"])
        self.date_selector_type.setMaximumWidth(80)
        grid.addWidget(self.date_selector_label, 4, 0)
        grid.addWidget(self.date_selector_edit, 4, 1)
        grid.addWidget(self.date_selector_type, 4, 2)
        
        # 来源选择器
        self.source_selector_label = QLabel("来源:")
        self.source_selector_edit = QLineEdit()
        self.source_selector_type = QComboBox()
        self.source_selector_type.addItems(["CSS", "XPath"])
        self.source_selector_type.setMaximumWidth(80)
        grid.addWidget(self.source_selector_label, 5, 0)
        grid.addWidget(self.source_selector_edit, 5, 1)
        grid.addWidget(self.source_selector_type, 5, 2)
        
        group.setLayout(grid)
        self.left_layout.addWidget(group)

    def create_advanced_settings(self):
        """创建高级设置页面"""
        # 并发设置组
        concurrent_group = QGroupBox("并发设置")
        concurrent_layout = QGridLayout()

        # 启用并发
        concurrent_layout.addWidget(QLabel("启用并发:"), 0, 0)
        self.enable_concurrent_checkbox = QCheckBox()
        self.enable_concurrent_checkbox.setChecked(True)
        concurrent_layout.addWidget(self.enable_concurrent_checkbox, 0, 1)

        # 并发线程数
        concurrent_layout.addWidget(QLabel("线程数:"), 1, 0)
        self.thread_count_spin = QSpinBox()
        self.thread_count_spin.setRange(1, 20)
        self.thread_count_spin.setValue(5)
        concurrent_layout.addWidget(self.thread_count_spin, 1, 1)
        # 兼容性别名
        self.max_workers_edit = self.thread_count_spin

        # 下载间隔
        concurrent_layout.addWidget(QLabel("下载间隔(秒):"), 2, 0)
        self.interval_spin = QDoubleSpinBox()
        self.interval_spin.setRange(0.0, 10.0)
        self.interval_spin.setSingleStep(0.1)
        self.interval_spin.setValue(0.5)
        concurrent_layout.addWidget(self.interval_spin, 2, 1)
        # 兼容性别名
        self.interval_edit = self.interval_spin

        # 重试次数
        concurrent_layout.addWidget(QLabel("重试次数:"), 3, 0)
        self.retry_count_spin = QSpinBox()
        self.retry_count_spin.setRange(0, 5)
        self.retry_count_spin.setValue(2)
        concurrent_layout.addWidget(self.retry_count_spin, 3, 1)
        # 兼容性别名
        self.retry_edit = self.retry_count_spin

        concurrent_group.setLayout(concurrent_layout)
        self.advanced_layout.addWidget(concurrent_group)

        # 文件设置组
        file_group = QGroupBox("文件设置")
        file_layout = QGridLayout()

        # 文件格式
        file_layout.addWidget(QLabel("文件格式:"), 0, 0)
        self.file_format_combo = QComboBox()
        self.file_format_combo.addItems(["CSV", "Excel"])
        file_layout.addWidget(self.file_format_combo, 0, 1)

        # 导出文件名
        file_layout.addWidget(QLabel("导出文件名:"), 1, 0)
        self.export_filename_edit = QLineEdit()
        self.export_filename_edit.setPlaceholderText("留空使用默认名称")
        file_layout.addWidget(self.export_filename_edit, 1, 1)

        # 分类ID
        file_layout.addWidget(QLabel("分类ID:"), 2, 0)
        self.classid_edit = QLineEdit()
        self.classid_edit.setPlaceholderText("文章分类标识")
        file_layout.addWidget(self.classid_edit, 2, 1)

        file_group.setLayout(file_layout)
        self.advanced_layout.addWidget(file_group)

        # 其他设置组
        other_group = QGroupBox("其他设置")
        other_layout = QGridLayout()

        # 收集链接
        other_layout.addWidget(QLabel("收集链接:"), 0, 0)
        self.collect_links_checkbox = QCheckBox()
        self.collect_links_checkbox.setChecked(True)
        other_layout.addWidget(self.collect_links_checkbox, 0, 1)

        # 爬取模式
        other_layout.addWidget(QLabel("爬取模式:"), 1, 0)
        self.crawl_mode_combo = QComboBox()
        self.crawl_mode_combo.addItems(["balance", "fast", "thorough"])
        other_layout.addWidget(self.crawl_mode_combo, 1, 1)

        # URL模式
        other_layout.addWidget(QLabel("URL模式:"), 2, 0)
        self.url_mode_combo = QComboBox()
        self.url_mode_combo.addItems(["absolute", "relative"])
        other_layout.addWidget(self.url_mode_combo, 2, 1)

        # 内容过滤按钮
        self.filter_button = QPushButton("设置内容过滤")
        self.filter_button.setObjectName("actionButton")
        self.filter_button.clicked.connect(self.set_content_filters)
        other_layout.addWidget(self.filter_button, 3, 0, 1, 2)

        other_group.setLayout(other_layout)
        self.advanced_layout.addWidget(other_group)

        # 添加弹性空间
        self.advanced_layout.addStretch()

    def create_pagination_settings(self):
        """创建动态翻页设置页面"""
        # 翻页类型选择组
        pagination_type_group = QGroupBox("翻页类型设置")
        pagination_type_layout = QGridLayout()

        # 翻页类型
        pagination_type_layout.addWidget(QLabel("翻页类型:"), 0, 0)
        self.pagination_type_combo = QComboBox()
        self.pagination_type_combo.addItems([
            "禁用动态翻页",
            "点击翻页",
            "滚动翻页",
            "iframe翻页"
        ])
        self.pagination_type_combo.setToolTip(
            "禁用动态翻页: 使用传统URL翻页\n"
            "点击翻页: 点击下一页按钮进行翻页\n"
            "滚动翻页: 通过滚动加载更多内容\n"
            "iframe翻页: 处理iframe内的翻页"
        )
        self.pagination_type_combo.currentTextChanged.connect(self.on_pagination_type_changed)
        pagination_type_layout.addWidget(self.pagination_type_combo, 0, 1, 1, 2)

        pagination_type_group.setLayout(pagination_type_layout)
        self.pagination_layout.addWidget(pagination_type_group)

        # 点击翻页设置组
        self.click_pagination_group = QGroupBox("点击翻页设置")
        click_layout = QGridLayout()

        # 下一页按钮选择器
        click_layout.addWidget(QLabel("下一页按钮选择器:"), 0, 0)
        self.next_button_selector_edit = QLineEdit()
        self.next_button_selector_edit.setPlaceholderText("例如: a.next:not(.lose)")
        self.next_button_selector_edit.setText("a.next:not(.lose)")
        click_layout.addWidget(self.next_button_selector_edit, 0, 1, 1, 2)

        # 内容就绪选择器
        click_layout.addWidget(QLabel("内容就绪选择器:"), 1, 0)
        self.content_ready_selector_edit = QLineEdit()
        self.content_ready_selector_edit.setPlaceholderText("等待内容加载的选择器(可选)")
        click_layout.addWidget(self.content_ready_selector_edit, 1, 1, 1, 2)

        # 最大翻页数
        click_layout.addWidget(QLabel("最大翻页数:"), 2, 0)
        self.max_pages_spin = QSpinBox()
        self.max_pages_spin.setRange(1, 100)
        self.max_pages_spin.setValue(10)
        click_layout.addWidget(self.max_pages_spin, 2, 1)

        # 点击后等待时间
        click_layout.addWidget(QLabel("点击后等待(毫秒):"), 2, 2)
        self.wait_after_click_spin = QSpinBox()
        self.wait_after_click_spin.setRange(500, 10000)
        self.wait_after_click_spin.setValue(1500)
        click_layout.addWidget(self.wait_after_click_spin, 2, 3)

        # 超时时间
        click_layout.addWidget(QLabel("超时时间(毫秒):"), 3, 0)
        self.click_timeout_spin = QSpinBox()
        self.click_timeout_spin.setRange(5000, 60000)
        self.click_timeout_spin.setValue(10000)
        click_layout.addWidget(self.click_timeout_spin, 3, 1)

        # 禁用检查
        click_layout.addWidget(QLabel("检查按钮禁用:"), 3, 2)
        self.disabled_check_checkbox = QCheckBox()
        self.disabled_check_checkbox.setChecked(True)
        self.disabled_check_checkbox.setToolTip("检查下一页按钮是否被禁用")
        click_layout.addWidget(self.disabled_check_checkbox, 3, 3)

        self.click_pagination_group.setLayout(click_layout)
        self.pagination_layout.addWidget(self.click_pagination_group)

        # 滚动翻页设置组
        self.scroll_pagination_group = QGroupBox("滚动翻页设置")
        scroll_layout = QGridLayout()

        # 滚动容器选择器
        scroll_layout.addWidget(QLabel("滚动容器选择器:"), 0, 0)
        self.scroll_container_selector_edit = QLineEdit()
        self.scroll_container_selector_edit.setPlaceholderText("例如: #largeData")
        self.scroll_container_selector_edit.setText("#largeData")
        scroll_layout.addWidget(self.scroll_container_selector_edit, 0, 1, 1, 2)

        # 滚动步长
        scroll_layout.addWidget(QLabel("滚动步长(像素):"), 1, 0)
        self.scroll_step_spin = QSpinBox()
        self.scroll_step_spin.setRange(100, 2000)
        self.scroll_step_spin.setValue(500)
        scroll_layout.addWidget(self.scroll_step_spin, 1, 1)

        # 滚动延迟
        scroll_layout.addWidget(QLabel("滚动延迟(毫秒):"), 1, 2)
        self.scroll_delay_spin = QSpinBox()
        self.scroll_delay_spin.setRange(500, 5000)
        self.scroll_delay_spin.setValue(1000)
        scroll_layout.addWidget(self.scroll_delay_spin, 1, 3)

        # 最大滚动次数
        scroll_layout.addWidget(QLabel("最大滚动次数:"), 2, 0)
        self.max_scrolls_spin = QSpinBox()
        self.max_scrolls_spin.setRange(5, 100)
        self.max_scrolls_spin.setValue(20)
        scroll_layout.addWidget(self.max_scrolls_spin, 2, 1)

        # 加载指示器选择器
        scroll_layout.addWidget(QLabel("加载指示器选择器:"), 3, 0)
        self.load_indicator_selector_edit = QLineEdit()
        self.load_indicator_selector_edit.setPlaceholderText("加载指示器选择器(可选)")
        scroll_layout.addWidget(self.load_indicator_selector_edit, 3, 1, 1, 2)

        # 滚动超时时间
        scroll_layout.addWidget(QLabel("滚动超时(毫秒):"), 4, 0)
        self.scroll_timeout_spin = QSpinBox()
        self.scroll_timeout_spin.setRange(5000, 60000)
        self.scroll_timeout_spin.setValue(10000)
        scroll_layout.addWidget(self.scroll_timeout_spin, 4, 1)

        # 高度容忍值
        scroll_layout.addWidget(QLabel("高度容忍值(像素):"), 4, 2)
        self.height_tolerance_spin = QSpinBox()
        self.height_tolerance_spin.setRange(10, 200)
        self.height_tolerance_spin.setValue(50)
        scroll_layout.addWidget(self.height_tolerance_spin, 4, 3)

        self.scroll_pagination_group.setLayout(scroll_layout)
        self.pagination_layout.addWidget(self.scroll_pagination_group)

        # iframe翻页设置组
        self.iframe_pagination_group = QGroupBox("iframe翻页设置")
        iframe_layout = QGridLayout()

        # iframe选择器
        iframe_layout.addWidget(QLabel("iframe选择器:"), 0, 0)
        self.iframe_selector_edit = QLineEdit()
        self.iframe_selector_edit.setPlaceholderText("例如: #content-frame")
        iframe_layout.addWidget(self.iframe_selector_edit, 0, 1, 1, 2)

        # iframe内翻页类型
        iframe_layout.addWidget(QLabel("iframe内翻页类型:"), 1, 0)
        self.iframe_pagination_type_combo = QComboBox()
        self.iframe_pagination_type_combo.addItems(["click", "scroll"])
        iframe_layout.addWidget(self.iframe_pagination_type_combo, 1, 1)

        self.iframe_pagination_group.setLayout(iframe_layout)
        self.pagination_layout.addWidget(self.iframe_pagination_group)

        # 测试按钮
        test_button_layout = QHBoxLayout()
        self.test_pagination_button = QPushButton("测试翻页设置")
        self.test_pagination_button.setObjectName("actionButton")
        self.test_pagination_button.clicked.connect(self.test_pagination_settings)
        test_button_layout.addWidget(self.test_pagination_button)
        test_button_layout.addStretch()
        self.pagination_layout.addLayout(test_button_layout)

        # 添加弹性空间
        self.pagination_layout.addStretch()

        # 初始化显示状态
        self.on_pagination_type_changed()

    def create_control_area(self):
        """创建右侧控制区域（配置组选择和操作按钮）"""
        control_group = QGroupBox("操作控制")
        control_layout = QVBoxLayout()

        # 配置组选择行
        config_row = QHBoxLayout()
        config_row.addWidget(QLabel("当前配置组:"))
        self.current_config_label = QLabel("未选择")
        self.current_config_label.setObjectName("configLabel")
        config_row.addWidget(self.current_config_label)
        config_row.addStretch()
        control_layout.addLayout(config_row)

        # 操作按钮行
        button_row = QHBoxLayout()

        # 开始爬取按钮
        self.start_button = QPushButton("开始爬取")
        self.start_button.setObjectName("startButton")
        self.start_button.clicked.connect(self.start_crawling)
        button_row.addWidget(self.start_button)

        # 停止按钮
        self.stop_button = QPushButton("停止")
        self.stop_button.setObjectName("stopButton")
        self.stop_button.clicked.connect(self.stop_crawling)
        self.stop_button.setEnabled(False)
        button_row.addWidget(self.stop_button)

        # 保存配置按钮
        self.save_button = QPushButton("保存配置")
        self.save_button.setObjectName("saveButton")
        self.save_button.clicked.connect(self.save_config)
        button_row.addWidget(self.save_button)

        control_layout.addLayout(button_row)
        control_group.setLayout(control_layout)
        self.right_layout.addWidget(control_group)
    
    def create_progress_bar(self):
        """创建进度条（放在右侧日志区域下方）"""
        # 进度条
        self.progress_label = QLabel("进度:")
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setObjectName("progressBar")

        progress_layout = QHBoxLayout()
        progress_layout.setSpacing(10)
        progress_layout.addWidget(self.progress_label)
        progress_layout.addWidget(self.progress_bar)

        # 添加到右侧布局
        self.right_layout.addLayout(progress_layout)

    def create_log_area(self):
        """创建日志区域（放在右侧）"""
        group = QGroupBox("运行日志")
        layout = QVBoxLayout()

        self.log_area = QTextEdit()
        self.log_area.setObjectName("logArea")
        self.log_area.setReadOnly(True)
        self.log_area.setStyleSheet("font-size:13px;")
        self.log_area.setMinimumHeight(300)  # 增加高度
        layout.addWidget(self.log_area)

        group.setLayout(layout)
        self.right_layout.addWidget(group)

    # 旧的控制按钮方法已移动到create_control_area中
    # def create_control_buttons(self):
    #     """创建控制按钮区域"""
    #     pass

    def start_crawling(self):
        """开始爬取（新的方法名）"""
        self.start_crawler()

    def stop_crawling(self):
        """停止爬取（新的方法名）"""
        self.stop_crawler()

    def update_current_config_label(self):
        """更新当前配置组标签"""
        current_config = self.config_combo.currentText()
        if current_config:
            self.current_config_label.setText(current_config)
        else:
            self.current_config_label.setText("未选择")

    def on_config_changed(self):
        """配置组改变时的处理"""
        self.config_group_changed()
        self.update_current_config_label()

    def start_ai_config(self):
        """启动AI智能配置（新方法名）"""
        self.ai_auto_config()

    def create_new_config(self):
        """创建新配置（新方法名）"""
        self.save_config_as()

    def set_content_filters(self):
        """设置内容过滤器"""
        # 这里可以添加内容过滤器的设置逻辑
        # 暂时显示一个消息框
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "内容过滤", "内容过滤器功能正在开发中...")

    def update_filename_placeholder(self):
        """更新文件名占位符"""
        if hasattr(self, 'file_format_combo') and hasattr(self, 'export_filename_edit'):
            format_text = self.file_format_combo.currentText()
            if format_text == "Excel":
                self.export_filename_edit.setPlaceholderText("例如: 文章数据.xlsx")
            else:
                self.export_filename_edit.setPlaceholderText("例如: 文章数据.csv")

    def ai_auto_config(self):
        """AI智能分析input_url并自动填充选择器（多线程，解耦分析逻辑）"""
        url = self.input_url_edit.text().strip()
        if not url:
            QMessageBox.warning(self, "输入错误", "请输入要分析的网址")
            return
        self.ai_button.setEnabled(False)
        self.log_message(f"AI分析中: {url}")
        self.ai_thread = AIConfigThread(url)
        self.ai_thread.result_signal.connect(self._ai_config_fill)
        self.ai_thread.error_signal.connect(self._ai_config_error)
        self.ai_thread.finished.connect(lambda: self.ai_button.setEnabled(True))
        self.ai_thread.start()

    def _ai_config_fill(self, result_dict):
        msg = []
        for k, v in result_dict.items():
            if k == '__article_url':
                msg.append(f"详情页分析URL: {v}")
            else:
                msg.append(f"{k} = {v}")
        self.log_message("AI分析结果:\n" + '\n'.join(msg))
        self._fill_ai_config_vars(result_dict)

    def _fill_ai_config_vars(self, result_dict):
        """自动填充AI分析得到的变量到界面"""
        mapping = {
            'list_container_selector': self.list_container_edit,
            'article_item_selector': self.article_item_edit,
            'title_selector': self.title_selector_edit,
            'content_selectors': self.content_selector_edit,
            'date_selector': self.date_selector_edit,
            'source_selector': self.source_selector_edit,
        }
        for k, widget in mapping.items():
            if k in result_dict:
                widget.setText(result_dict[k])

    def _ai_config_error(self, msg):
        self.log_message(msg)
    
    def load_config(self):
        """加载配置到界面"""
        # 更新配置组下拉框
        self.config_combo.clear()
        groups = self.config_manager.get_groups()
        self.config_combo.addItems(groups)
        
        # 设置当前选中的配置组
        current_group = self.config_manager.get_current_group()
        if current_group in groups:
            index = groups.index(current_group)
            self.config_combo.setCurrentIndex(index)
        else:
            self.config_combo.setCurrentIndex(0)
        
        # 加载当前配置组的数据
        self.config_group_changed()
    
    def config_group_changed(self):
        """当配置组改变时，加载对应的配置"""
        group_name = self.config_combo.currentText()
        group_config = self.config_manager.get_group(group_name)
        
        if group_config:
            # 更新UI控件
            self.input_url_edit.setText(group_config.get("input_url", ""))
            self.base_url_edit.setText(group_config.get("base_url", ""))
            self.max_pages_edit.setText(str(group_config.get("max_pages", "3")))
            self.list_container_edit.setText(group_config.get("list_container_selector", ""))
            self.list_container_type.setCurrentIndex(0 if group_config.get("list_container_type", "CSS") == "CSS" else 1)
            self.article_item_edit.setText(group_config.get("article_item_selector", ""))
            self.article_item_type.setCurrentIndex(0 if group_config.get("article_item_type", "CSS") == "CSS" else 1)
            self.title_selector_edit.setText(group_config.get("title_selector", ""))
            self.title_selector_type.setCurrentIndex(0 if group_config.get("title_selector_type", "CSS") == "CSS" else 1)
            self.content_selector_edit.setText(group_config.get("content_selectors", ""))
            self.content_selector_type.setCurrentIndex(0 if group_config.get("content_type", "CSS") == "CSS" else 1)
            self.date_selector_edit.setText(group_config.get("date_selector", ""))
            self.date_selector_type.setCurrentIndex(0 if group_config.get("date_selector_type", "CSS") == "CSS" else 1)
            self.source_selector_edit.setText(group_config.get("source_selector", ""))
            self.source_selector_type.setCurrentIndex(0 if group_config.get("source_selector_type", "CSS") == "CSS" else 1)
            self.page_suffix_edit.setText(group_config.get("page_suffix", "index_{n}.html"))
            if hasattr(self, 'page_suffix_start_edit'):
                self.page_suffix_start_edit.setText(str(group_config.get("page_suffix_start", "1")))
            if hasattr(self, 'url_mode_combo'):
                self.url_mode_combo.setCurrentIndex(
                    {"absolute": 0, "relative": 1}.get(group_config.get("url_mode", "absolute"), 0)
                )
            if hasattr(self, 'page_load_strategy_combo'):
                self.page_load_strategy_combo.setCurrentIndex(
                    {"normal": 0, "eager": 1, "none": 2}.get(group_config.get("page_load_strategy", "normal"), 0)
                )
            # 新增：采集图片与附件链接
            if hasattr(self, 'collect_links_checkbox'):
                self.collect_links_checkbox.setChecked(group_config.get("collect_links", True))
            # 新增：导出文件名和classid
            if hasattr(self, 'export_filename_edit'):
                self.export_filename_edit.setText(group_config.get("export_filename", ""))
            if hasattr(self, 'classid_edit'):
                self.classid_edit.setText(group_config.get("classid", ""))
            # 新增：文件格式
            if hasattr(self, 'file_format_combo'):
                file_format = group_config.get("file_format", "CSV")
                format_index = 0 if file_format == "CSV" else 1
                self.file_format_combo.setCurrentIndex(format_index)
            # 新增：并发线程数
            if hasattr(self, 'thread_count_spin'):
                self.thread_count_spin.setValue(group_config.get("max_workers", 5))
            # 新增：重试次数
            if hasattr(self, 'retry_count_spin'):
                self.retry_count_spin.setValue(group_config.get("retry", 3))
            # 新增：下载间隔
            if hasattr(self, 'interval_spin'):
                self.interval_spin.setValue(group_config.get("interval", 0.5))

            # 加载动态翻页配置
            self.load_pagination_config(group_config.get("pagination_config", {}))

            # 设置当前配置组
            self.config_manager.set_current_group(group_name)
    
    def save_config(self):
        """保存当前配置到选中的配置组"""
        group_name = self.config_combo.currentText()
        config_data = self.get_current_config()
        
        self.config_manager.add_group(group_name, config_data)
        self.log_message(f"配置 '{group_name}' 已保存")
        QMessageBox.information(self, "保存成功", f"配置已保存到 '{group_name}'")
    
    def save_config_as(self):
        """将当前配置保存为新的配置组"""
        # 获取新配置组名称
        new_name, ok = QInputDialog.getText(
            self, 
            "新建配置组", 
            "请输入配置组名称:", 
            QLineEdit.Normal
        )
        
        if ok and new_name:
            # 检查名称是否已存在
            groups = self.config_manager.get_groups()
            if new_name in groups:
                reply = QMessageBox.question(
                    self, 
                    "确认覆盖", 
                    f"配置组 '{new_name}' 已存在，是否覆盖?",
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    return
            
            # 获取当前配置
            config_data = self.get_current_config()
            
            # 保存新配置组
            self.config_manager.add_group(new_name, config_data)
            
            # 更新下拉框
            self.config_combo.clear()
            self.config_combo.addItems(self.config_manager.get_groups())
            self.config_combo.setCurrentText(new_name)
            
            self.log_message(f"新配置 '{new_name}' 已保存")
            QMessageBox.information(self, "保存成功", f"新配置 '{new_name}' 已创建")
    
    def delete_config(self):
        """删除选中的配置组"""
        group_name = self.config_combo.currentText()
        
        # 不能删除默认配置
        if group_name == "default":
            QMessageBox.warning(self, "操作禁止", "不能删除默认配置组")
            return
            
        reply = QMessageBox.question(
            self, 
            "确认删除", 
            f"确定要删除配置组 '{group_name}' 吗?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.config_manager.delete_group(group_name):
                # 更新下拉框
                groups = self.config_manager.get_groups()
                self.config_combo.clear()
                self.config_combo.addItems(groups)
                
                # 设置当前配置组
                if groups:
                    self.config_combo.setCurrentIndex(0)
                
                self.log_message(f"配置组 '{group_name}' 已删除")
            else:
                QMessageBox.warning(self, "删除失败", f"配置组 '{group_name}' 删除失败")
    
    def get_current_config(self):
        url_mode_map = {0: "absolute", 1: "relative"}
        config = {
            "input_url": self.input_url_edit.text().strip(),
            "base_url": self.base_url_edit.text().strip(),
            "max_pages": self.max_pages_edit.text().strip(),
            "list_container_selector": self.list_container_edit.text().strip(),
            "list_container_type": self.list_container_type.currentText(),
            "article_item_selector": self.article_item_edit.text().strip(),
            "article_item_type": self.article_item_type.currentText(),
            "title_selector": self.title_selector_edit.text().strip(),
            "title_selector_type": self.title_selector_type.currentText(),
            "content_selectors": self.content_selector_edit.text().strip(),
            "content_type": self.content_selector_type.currentText(),
            "date_selector": self.date_selector_edit.text().strip(),
            "date_selector_type": self.date_selector_type.currentText(),
            "source_selector": self.source_selector_edit.text().strip(),
            "source_selector_type": self.source_selector_type.currentText(),
            "page_suffix": self.page_suffix_edit.text().strip() or "index_{n}.html",
            "page_suffix_start": int(self.page_suffix_start_edit.text().strip() or 1) if hasattr(self, 'page_suffix_start_edit') else 1,
            "dynamic_pagination_type": self.get_dynamic_pagination_type(),
            # 新的动态翻页配置
            "pagination_config": self.get_pagination_config() if hasattr(self, 'pagination_type_combo') else {},
            "url_mode": url_mode_map.get(self.url_mode_combo.currentIndex(), "absolute") if hasattr(self, 'url_mode_combo') else "absolute",
            "browser": self.browser_combo.currentText() if hasattr(self, 'browser_combo') else "Firefox",
            "headless": self.headless_combo.currentIndex() == 0 if hasattr(self, 'headless_combo') else True,  # 是为True
            "window_size": self.window_size_edit.text().strip() if hasattr(self, 'window_size_edit') else "1200,800",
            "page_load_strategy": self.page_load_strategy_combo.currentText() if hasattr(self, 'page_load_strategy_combo') else "normal",
            "collect_links": self.collect_links_checkbox.isChecked() if hasattr(self, 'collect_links_checkbox') else True,
            # 新增：采集模式
            "mode": self.get_crawl_mode(),
            "filters": self.filters if hasattr(self, 'filters') else [],
            # 新增：导出文件名
            "export_filename": self.export_filename_edit.text().strip() if hasattr(self, 'export_filename_edit') else "",
            "classid": self.classid_edit.text().strip() if hasattr(self, 'classid_edit') else "",
            # 新增：文件格式
            "file_format": self.file_format_combo.currentText() if hasattr(self, 'file_format_combo') else "CSV",
            # 新增：并发线程数
            "max_workers": self.thread_count_spin.value() if hasattr(self, 'thread_count_spin') else 5,
            # 新增：重试次数
            "retry": self.retry_count_spin.value() if hasattr(self, 'retry_count_spin') else 3,
            # 新增：下载间隔
            "interval": self.interval_spin.value() if hasattr(self, 'interval_spin') else 0.5,
        }
        return config

    def load_pagination_config(self, pagination_config):
        """加载动态翻页配置到界面"""
        if not hasattr(self, 'pagination_type_combo'):
            return

        pagination_type = pagination_config.get('pagination_type', '禁用动态翻页')

        # 设置翻页类型
        type_index = self.pagination_type_combo.findText(pagination_type)
        if type_index >= 0:
            self.pagination_type_combo.setCurrentIndex(type_index)

        # 加载点击翻页配置
        if pagination_type == "点击翻页":
            if hasattr(self, 'next_button_selector_edit'):
                self.next_button_selector_edit.setText(pagination_config.get('next_button_selector', 'a.next:not(.lose)'))
            if hasattr(self, 'content_ready_selector_edit'):
                self.content_ready_selector_edit.setText(pagination_config.get('content_ready_selector', ''))
            if hasattr(self, 'max_pages_spin'):
                self.max_pages_spin.setValue(pagination_config.get('max_pages', 10))
            if hasattr(self, 'wait_after_click_spin'):
                self.wait_after_click_spin.setValue(pagination_config.get('wait_after_click', 1500))
            if hasattr(self, 'click_timeout_spin'):
                self.click_timeout_spin.setValue(pagination_config.get('timeout', 10000))
            if hasattr(self, 'disabled_check_checkbox'):
                self.disabled_check_checkbox.setChecked(pagination_config.get('disabled_check', True))

        # 加载滚动翻页配置
        elif pagination_type == "滚动翻页":
            if hasattr(self, 'scroll_container_selector_edit'):
                self.scroll_container_selector_edit.setText(pagination_config.get('scroll_container_selector', '#largeData'))
            if hasattr(self, 'scroll_step_spin'):
                self.scroll_step_spin.setValue(pagination_config.get('scroll_step', 500))
            if hasattr(self, 'scroll_delay_spin'):
                self.scroll_delay_spin.setValue(pagination_config.get('scroll_delay', 1000))
            if hasattr(self, 'max_scrolls_spin'):
                self.max_scrolls_spin.setValue(pagination_config.get('max_scrolls', 20))
            if hasattr(self, 'load_indicator_selector_edit'):
                self.load_indicator_selector_edit.setText(pagination_config.get('load_indicator_selector', ''))
            if hasattr(self, 'scroll_timeout_spin'):
                self.scroll_timeout_spin.setValue(pagination_config.get('scroll_timeout', 10000))
            if hasattr(self, 'height_tolerance_spin'):
                self.height_tolerance_spin.setValue(pagination_config.get('height_tolerance', 50))

        # 加载iframe翻页配置
        elif pagination_type == "iframe翻页":
            if hasattr(self, 'iframe_selector_edit'):
                self.iframe_selector_edit.setText(pagination_config.get('iframe_selector', ''))
            if hasattr(self, 'iframe_pagination_type_combo'):
                iframe_type = pagination_config.get('iframe_pagination_type', 'click')
                iframe_index = self.iframe_pagination_type_combo.findText(iframe_type)
                if iframe_index >= 0:
                    self.iframe_pagination_type_combo.setCurrentIndex(iframe_index)

        # 更新界面显示
        self.on_pagination_type_changed()

    def get_crawl_mode(self):
        # 检查新的高级设置中的爬取模式组件
        if hasattr(self, 'crawl_mode_combo'):
            return self.crawl_mode_combo.currentText()
        # 检查旧布局中的模式组件
        elif hasattr(self, 'mode_combo'):
            mode_map = {"平衡模式": "balance", "快速模式": "fast", "安全模式": "safe"}
            return mode_map.get(self.mode_combo.currentText(), "balance")
        # 默认返回平衡模式
        else:
            return "balance"

    def get_dynamic_pagination_type(self):
        """获取动态翻页类型（兼容旧版本）"""
        if hasattr(self, 'dynamic_pagination_combo'):
            pagination_map = {
                "自动检测": None,  # None表示自动检测
                "传统翻页": "traditional",
                "JavaScript点击": "javascript_click",
                "无限滚动": "infinite_scroll",
                "下拉选择": "dropdown_pagination"
            }
            return pagination_map.get(self.dynamic_pagination_combo.currentText(), None)
        else:
            # 如果没有旧的组件，返回None使用自动检测
            return None

    def get_dynamic_pagination_type_from_config(self, pagination_config):
        """从新的翻页配置转换为旧的动态翻页类型"""
        if not pagination_config or not pagination_config.get('enabled', False):
            return None

        pagination_type = pagination_config.get('pagination_type', '禁用动态翻页')

        # 转换映射
        type_map = {
            "禁用动态翻页": None,
            "点击翻页": "javascript_click",
            "滚动翻页": "infinite_scroll",
            "iframe翻页": "javascript_click"  # iframe翻页也使用点击方式
        }

        return type_map.get(pagination_type, None)
    
    def start_crawler(self):
        config_data = self.get_current_config()
        diver = {"headless": config_data["headless"]}
        if config_data["window_size"]:
            diver["window_size"] = config_data["window_size"]
        if config_data["page_load_strategy"]:
            diver["page_load_strategy"] = config_data["page_load_strategy"]
        crawler_config = {
            'input_url': config_data['input_url'],
            'base_url': config_data['base_url'],
            'max_pages': int(config_data['max_pages']) if config_data['max_pages'] else None,
            'list_container_selector': config_data['list_container_selector'],
            'list_container_type': config_data['list_container_type'],
            'article_item_selector': config_data['article_item_selector'],
            'article_item_type': config_data['article_item_type'],
            'title_selector': config_data['title_selector'],
            'title_selector_type': config_data['title_selector_type'],
            'content_selectors': [s.strip() for s in config_data['content_selectors'].split(';') if s.strip()],
            'content_type': config_data['content_type'],
            'date_selector': config_data['date_selector'],
            'date_selector_type': config_data['date_selector_type'],
            'source_selector': config_data['source_selector'],
            'source_selector_type': config_data['source_selector_type'],
            'page_suffix': config_data['page_suffix'],
            'page_suffix_start': config_data['page_suffix_start'],
            # 动态翻页类型（优先使用新配置，回退到旧配置）
            'dynamic_pagination_type': self.get_dynamic_pagination_type_from_config(config_data.get('pagination_config', {})) or config_data.get('dynamic_pagination_type'),
            'url_mode': config_data['url_mode'],
            'browser': config_data['browser'],
            'diver': diver,
            'collect_links': config_data.get('collect_links', True),
            # 新增：模式参数
            'mode': config_data.get('mode', 'balance'),
            # 新增：导出文件名参数
            'export_filename': config_data.get('export_filename', None),
            # 新增：文件格式参数
            'file_format': config_data.get('file_format', 'CSV'),
            # 新增：并发线程数
            'max_workers': config_data.get('max_workers', 5),
            # 新增：重试次数
            'retry': config_data.get('retry', 3),
            # 新增：下载间隔
            'interval': config_data.get('interval', 0.5),
            # 新增：classid参数
            'classid': config_data.get('classid', ''),
        }
        # 验证输入
        if not crawler_config['input_url']:
            QMessageBox.warning(self, "输入错误", "请输入有效的URL")
            return
            
        if not crawler_config['content_selectors']:
            QMessageBox.warning(self, "输入错误", "请输入正文内容选择器")
            return
            
        # 更新按钮状态
        if hasattr(self, 'start_button'):
            self.start_button.setEnabled(False)
        elif hasattr(self, 'run_button'):
            self.run_button.setEnabled(False)

        if hasattr(self, 'stop_button'):
            self.stop_button.setEnabled(True)
        self.progress_bar.setValue(0)
        
        # 记录开始信息
        self.log_message("="*50)
        self.log_message("开始爬取任务...")
        self.log_message(f"目标URL: {crawler_config['input_url']}")
        self.log_message(f"最大页数: {crawler_config['max_pages'] or '无限制'}")
        
        # 创建并启动爬虫线程
        self.crawler_thread = CrawlerThread(crawler_config)
        self.crawler_thread.log_signal.connect(self.log_message)
        self.crawler_thread.finished_signal.connect(self.crawler_finished)
        self.crawler_thread.start()
    
    def stop_crawler(self):
        if self.crawler_thread and self.crawler_thread.isRunning():
            self.log_message("用户请求停止爬取...")
            self.crawler_thread.terminate()
            self.crawler_thread.wait()
            self.log_message("爬取任务已终止")
            if hasattr(self, 'start_button'):
                self.start_button.setEnabled(True)
            elif hasattr(self, 'run_button'):
                self.run_button.setEnabled(True)

            if hasattr(self, 'stop_button'):
                self.stop_button.setEnabled(False)
    
    def crawler_finished(self, result):
        self.log_message("="*50)

        # 处理不同的返回结果格式
        if isinstance(result, dict):
            total = result.get('total', 0)
            success = result.get('success', 0)
            failed = result.get('failed', 0)

            self.log_message(f"爬取任务完成! 共处理 {total} 篇文章")
            self.log_message(f"成功: {success} 篇")
            self.log_message(f"失败: {failed} 篇")
        elif isinstance(result, (int, float)):
            # 如果返回的是数字（如翻页数量）
            self.log_message(f"任务完成! 处理了 {result} 页")
        else:
            # 其他情况
            self.log_message(f"任务完成! 结果: {result}")

        self.log_message("="*50)
        
        self.progress_bar.setValue(100)
        if hasattr(self, 'start_button'):
            self.start_button.setEnabled(True)
        elif hasattr(self, 'run_button'):
            self.run_button.setEnabled(True)

        if hasattr(self, 'stop_button'):
            self.stop_button.setEnabled(False)
        
        # 显示完成提示
        QMessageBox.information(self, "完成", f"爬取任务完成!\n成功: {result['success']}篇\n失败: {result['failed']}篇")
    
    def clear_log(self):
        self.log_area.clear()
    
    def log_message(self, message):
        """将消息添加到日志区域"""
        self.log_area.append(message)
        self.log_area.ensureCursorVisible()  # 自动滚动到底部
        
        # 更新进度条（如果消息包含进度信息）
        if "正在处理第" in message and "篇文章" in message:
            parts = message.split(":")
            if len(parts) > 0:
                progress_part = parts[0].split("第")[1].split("/")[0]
                total_part = parts[0].split("/")[1].split("篇")[0]
                try:
                    current = int(progress_part.strip())
                    total = int(total_part.strip())
                    progress = int((current / total) * 100) if total > 0 else 0
                    self.progress_bar.setValue(progress)
                except:
                    pass

    def update_link_button_text(self, checked):
        self.collect_links_checkbox.setText(f"采集图片与附件: {'开启' if checked else '关闭'}")

    def update_filename_placeholder(self):
        """根据选择的文件格式更新文件名占位符"""
        format_text = self.file_format_combo.currentText()
        if format_text == "Excel":
            self.export_filename_edit.setPlaceholderText("可选，如 test")
        else:  # CSV
            self.export_filename_edit.setPlaceholderText("可选，如 test")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = CrawlerGUI()
    window.show()
    sys.exit(app.exec_())
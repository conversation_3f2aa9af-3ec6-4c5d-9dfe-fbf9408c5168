import asyncio
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext
from typing import Optional, Dict, Any, Union, Callable, Awaitable
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger('PaginationHandler')

class PaginationHandler:
    """翻页处理器，支持多种动态翻页方式"""

    def __init__(self, page: Page):
        self.page = page

    async def debug_pagination_elements(self, selectors: list = None) -> dict:
        """
        调试翻页元素，检查页面上可能的翻页按钮
        :param selectors: 要检查的选择器列表，如果为None则使用默认列表
        :return: 调试信息字典
        """
        if selectors is None:
            selectors = [
                "a.next:not(.lose)",
                "a.page-link:has-text('Next')",
                "a[onclick*='page']",
                ".js_pageI:not(.cur)",
                ".pager a",
                "a:contains('下一页')",
                "a:contains('Next')",
                ".next-page"
            ]

        debug_info = {
            'found_elements': [],
            'page_url': self.page.url,
            'page_title': await self.page.title()
        }

        for selector in selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    for i, element in enumerate(elements):
                        text = await element.text_content()
                        is_visible = await element.is_visible()
                        is_enabled = await element.is_enabled()
                        debug_info['found_elements'].append({
                            'selector': selector,
                            'index': i,
                            'text': text.strip() if text else '',
                            'visible': is_visible,
                            'enabled': is_enabled
                        })
            except Exception as e:
                logger.warning(f"Error checking selector {selector}: {str(e)}")

        return debug_info
    
    async def click_pagination(
        self,
        next_button_selector: str,
        max_pages: int = 10,
        content_ready_selector: Optional[str] = None,
        timeout: int = 10000,
        wait_after_click: int = 1000,
        disabled_check: bool = True
    ) -> int:
        """
        点击翻页处理
        :param next_button_selector: 下一页按钮的CSS选择器
        :param max_pages: 最大翻页次数
        :param content_ready_selector: 内容加载完成的标识选择器
        :param timeout: 超时时间(毫秒)
        :param wait_after_click: 点击后等待的时间（毫秒）
        :param disabled_check: 是否检查按钮禁用状态
        :return: 实际翻页次数
        """
        current_page = 1
        while current_page < max_pages:
            # 等待内容加载
            if content_ready_selector:
                try:
                    await self.page.wait_for_selector(
                        content_ready_selector, 
                        state="attached",
                        timeout=timeout
                    )
                    logger.info(f"Page {current_page} content loaded")
                except Exception as e:
                    logger.warning(f"Content loading warning: {str(e)}")
            
            # 尝试点击下一页
            try:
                next_button = await self.page.wait_for_selector(
                    next_button_selector,
                    state="visible",
                    timeout=timeout  # 使用传入的timeout参数而不是硬编码的5000
                )

                # 检查按钮是否被禁用
                if disabled_check and await next_button.is_disabled():
                    logger.info("Reached last page (button disabled)")
                    break

                await next_button.click()
                current_page += 1
                logger.info(f"Clicked to page {current_page}")

                # 等待页面稳定
                await self.page.wait_for_timeout(wait_after_click)
            except Exception as e:
                logger.error(f"Pagination error on page {current_page}: {str(e)}")
                # 如果是超时错误，提供更详细的信息
                if "Timeout" in str(e):
                    logger.info(f"Could not find next button with selector: {next_button_selector}")
                    logger.info("This might indicate we've reached the last page or the selector is incorrect")
                break
        
        return current_page - 1
    
    async def scroll_pagination(
        self,
        scroll_container_selector: str,
        scroll_step: int = 500,
        scroll_delay: int = 1000,
        max_scrolls: int = 20,
        load_indicator_selector: Optional[str] = None,
        scroll_timeout: int = 10000,  # 增加超时时间
        height_tolerance: int = 50
    ) -> int:
        """
        滚动翻页处理
        :param scroll_container_selector: 滚动容器的CSS选择器
        :param scroll_step: 每次滚动像素数
        :param scroll_delay: 滚动间隔(毫秒)
        :param max_scrolls: 最大滚动次数
        :param load_indicator_selector: 加载指示器的选择器
        :param scroll_timeout: 滚动加载超时时间
        :param height_tolerance: 高度变化容忍值
        :return: 实际滚动次数
        """
        scroll_count = 0
        last_height = 0
        
        # 获取滚动容器
        try:
            # 首先尝试等待容器可见
            container = await self.page.wait_for_selector(
                scroll_container_selector,
                state="attached",  # 改为attached状态，不要求可见
                timeout=scroll_timeout
            )

            # 检查容器是否隐藏，如果隐藏则尝试显示
            is_hidden = await container.is_hidden()
            if is_hidden:
                logger.warning(f"Scroll container {scroll_container_selector} is hidden, attempting to make it visible")
                # 尝试滚动到容器位置
                await container.scroll_into_view_if_needed()
                await self.page.wait_for_timeout(1000)

                # 再次检查是否可见
                is_hidden = await container.is_hidden()
                if is_hidden:
                    logger.warning("Container is still hidden, will attempt to scroll anyway")

        except Exception as e:
            logger.error(f"Scroll container not found: {str(e)}")
            logger.info(f"Attempted selector: {scroll_container_selector}")

            # 尝试备用选择器
            backup_selectors = ["body", "html", ".content", "#content", ".main"]
            for backup_selector in backup_selectors:
                try:
                    logger.info(f"Trying backup selector: {backup_selector}")
                    container = await self.page.wait_for_selector(backup_selector, timeout=2000)
                    logger.info(f"Successfully found backup container: {backup_selector}")
                    break
                except:
                    continue
            else:
                logger.error("No suitable scroll container found")
                return 0
        
        while scroll_count < max_scrolls:
            # 执行滚动
            await container.evaluate(f"container => container.scrollBy(0, {scroll_step})")
            await self.page.wait_for_timeout(scroll_delay)
            
            # 获取当前滚动高度
            new_height = await container.evaluate("container => container.scrollHeight")
            
            # 检测是否滚动到底部
            if abs(new_height - last_height) <= height_tolerance:
                logger.info("No more content to load (scroll height unchanged)")
                break
                
            last_height = new_height
            scroll_count += 1
            logger.info(f"Scrolled {scroll_count} times, height: {new_height}px")
            
            # 等待加载完成
            if load_indicator_selector:
                try:
                    # 等待加载指示器出现
                    await self.page.wait_for_selector(
                        load_indicator_selector,
                        state="visible",
                        timeout=2000
                    )
                    # 等待加载指示器消失
                    await self.page.wait_for_selector(
                        load_indicator_selector,
                        state="hidden",
                        timeout=10000
                    )
                    logger.info("Load indicator processed")
                except Exception as e:
                    logger.warning(f"Load indicator error: {str(e)}")
        
        return scroll_count
    
    async def handle_iframe_pagination(
        self,
        iframe_selector: str,
        pagination_type: str,
        **kwargs
    ) -> int:
        """
        处理iframe内的翻页
        :param iframe_selector: iframe的CSS选择器
        :param pagination_type: 翻页类型 ('click' 或 'scroll')
        :param kwargs: 传递给翻页方法的参数
        :return: 实际翻页/滚动次数
        """
        try:
            # 定位iframe
            frame_element = await self.page.wait_for_selector(iframe_selector)
            frame = await frame_element.content_frame()
            
            if not frame:
                raise ValueError(f"Could not get frame from selector: {iframe_selector}")
            
            # 创建iframe内的页面处理器
            iframe_handler = PaginationHandler(frame)
            
            if pagination_type == 'click':
                return await iframe_handler.click_pagination(**kwargs)
            elif pagination_type == 'scroll':
                return await iframe_handler.scroll_pagination(**kwargs)
            else:
                raise ValueError("Unsupported pagination type. Use 'click' or 'scroll'")
        except Exception as e:
            logger.error(f"Iframe pagination error: {str(e)}")
            return 0

async def launch_browser(
    p,  # 新增参数
    headless: bool = False,
    browser_type: str = "chromium",
    slow_mo: int = 100,
    proxy: Optional[Dict] = None,
    viewport: Dict[str, int] = {"width": 1280, "height": 720},
    user_agent: Optional[str] = None,
    **launch_kwargs
) -> tuple[Browser, BrowserContext, Page]:
    """
    启动Playwright浏览器
    :param p: async_playwright 实例
    :param headless: 是否无头模式
    :param browser_type: 浏览器类型 (chromium, firefox, webkit)
    :param slow_mo: 操作延迟(毫秒)
    :param proxy: 代理设置 {'server': 'http://host:port'}
    :param viewport: 视口大小 {'width': 1280, 'height': 720}
    :param user_agent: 自定义User-Agent
    :return: (browser, context, page)
    """
    # 选择浏览器类型
    browser_launcher = getattr(p, browser_type).launch
    
    # 准备启动参数
    launch_options = {
        "headless": headless,
        "slow_mo": slow_mo,
        **launch_kwargs
    }
    
    # 添加代理设置
    if proxy:
        launch_options["proxy"] = proxy
    
    browser = await browser_launcher(**launch_options)
    
    # 创建上下文
    context_options = {"viewport": viewport}
    if user_agent:
        context_options["user_agent"] = user_agent
    
    context = await browser.new_context(**context_options)
    page = await context.new_page()
    
    return browser, context, page

# ==================== 测试函数 ====================

async def click_pagination(p, mode='balance', config_file='config.json'):
    """测试点击分页功能
    Args:
        p: async_playwright 实例
        mode: 采集模式 (fast/safe/balance)
        config_file: 配置文件路径
    """
    browser, context, page = await launch_browser(p, headless=False)
    handler = PaginationHandler(page)
    
    # 使用测试网站（实际使用时替换为真实网站）
    await page.goto(
        "http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign=",
        timeout=60000,
        wait_until="domcontentloaded"
    )
    
    # 执行翻页
    pages_processed = await handler.click_pagination(
        next_button_selector="a.next:not(.lose)",
        content_ready_selector="",
        max_pages=5,
        wait_after_click=1500
    )
    
    logger.info(f"Processed {pages_processed} pages")
    
    # 截图保存结果
    await page.screenshot(path="click_pagination_result.png")
    await context.close()
    await browser.close()
    return pages_processed

async def scroll_pagination(p):
    """测试滚动翻页功能"""
    logger.info("Starting scroll pagination test...")
    browser, context, page = await launch_browser(p, headless=False)
    handler = PaginationHandler(page)
    
    # 使用测试网站（实际使用时替换为真实网站）
    await page.goto("https://www.shrd.gov.cn/n8347/n8403/index.html")
    
    # 执行滚动翻页
    scrolls_processed = await handler.scroll_pagination(
        scroll_container_selector="#largeData",
        scroll_step=800,
        scroll_delay=1500,
        max_scrolls=10,
        load_indicator_selector="#load1"
    )
    
    logger.info(f"Performed {scrolls_processed} scrolls")
    
    # 截图保存结果
    await page.screenshot(path="scroll_pagination_result.png")
    await context.close()
    await browser.close()
    return scrolls_processed

async def iframe_pagination(p):
    """测试iframe内翻页功能"""
    logger.info("Starting iframe pagination test...")
    browser, context, page = await launch_browser(p, headless=False)
    handler = PaginationHandler(page)

    # 使用包含iframe的测试页面，加载一个实际有分页的页面
    await page.set_content("""
    <html>
        <body>
            <h1>Main Page</h1>
            <iframe id="content-frame" src="http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign="></iframe>
        </body>
    </html>
    """)

    # 等待iframe加载
    try:
        await page.wait_for_selector("#content-frame", timeout=10000)
        await page.wait_for_timeout(3000)  # 等待iframe内容加载

        # 在iframe内执行翻页
        pages_processed = await handler.handle_iframe_pagination(
            iframe_selector="#content-frame",
            pagination_type="click",
            next_button_selector="a.next:not(.lose)",  # 使用与主测试相同的选择器
            max_pages=2  # 减少测试页数
        )

        logger.info(f"Processed {pages_processed} pages in iframe")
    except Exception as e:
        logger.warning(f"Iframe pagination test failed: {str(e)}")
        logger.info("This is expected if the iframe content doesn't load properly in test environment")
        pages_processed = 0

    # 截图保存结果
    await page.screenshot(path="iframe_pagination_result.png")
    await context.close()
    await browser.close()
    return pages_processed

async def main():
    import argparse
    parser = argparse.ArgumentParser(description='分页处理器测试参数')
    parser.add_argument('--mode', choices=['fast', 'safe', 'balance'], default='balance',
                        help='采集模式选择 (default: balance)')
    parser.add_argument('--config', type=str, default='config.json',
                        help='配置文件路径 (default: config.json)')
    parser.add_argument('--test', choices=['click', 'scroll', 'iframe', 'all'], default='all',
                        help='选择要运行的测试 (default: all)')
    args = parser.parse_args()

    results = {}

    async with async_playwright() as p:
        if args.test in ['click', 'all']:
            try:
                results['click_pagination'] = await click_pagination(p, mode=args.mode, config_file=args.config)
            except Exception as e:
                logger.error(f"Click pagination test failed: {str(e)}")
                results['click_pagination'] = 0

        if args.test in ['scroll', 'all']:
            try:
                results['scroll_pagination'] = await scroll_pagination(p)
            except Exception as e:
                logger.error(f"Scroll pagination test failed: {str(e)}")
                results['scroll_pagination'] = 0

        if args.test in ['iframe', 'all']:
            try:
                results['iframe_pagination'] = await iframe_pagination(p)
            except Exception as e:
                logger.error(f"Iframe pagination test failed: {str(e)}")
                results['iframe_pagination'] = 0

    # 打印测试结果
    logger.info("\n===== Test Results =====")
    for test_name, result in results.items():
        logger.info(f"{test_name}: {result}")

    # 如果所有测试都失败，提供帮助信息
    if all(result == 0 for result in results.values()):
        logger.info("\n===== 故障排除建议 =====")
        logger.info("所有测试都失败了，可能的原因:")
        logger.info("1. 网络连接问题")
        logger.info("2. 目标网站结构发生变化")
        logger.info("3. 分页选择器不正确")
        logger.info("建议使用 debug_pagination.py 脚本进行详细调试")

if __name__ == "__main__":
    asyncio.run(main())
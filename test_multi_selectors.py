#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多选择器功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_multi_selector_config():
    """测试多选择器配置"""
    print("=" * 60)
    print("测试多选择器配置")
    print("=" * 60)
    
    # 模拟GUI配置
    gui_config = {
        'title_selector': 'h1.title; .article-title; .news-headline',
        'date_selector': '.publish-date; .article-date; time',
        'source_selector': '.source; .author; .publisher',
        'content_selectors': '.content; .article-content; .main-text'
    }
    
    print("GUI配置:")
    for key, value in gui_config.items():
        print(f"  {key}: {value}")
    
    # 模拟配置转换（类似 crawler_gui_2.py 中的逻辑）
    crawler_config = {
        'title_selectors': [s.strip() for s in gui_config['title_selector'].split(';') if s.strip()],
        'date_selectors': [s.strip() for s in gui_config['date_selector'].split(';') if s.strip()],
        'source_selectors': [s.strip() for s in gui_config['source_selector'].split(';') if s.strip()],
        'content_selectors': [s.strip() for s in gui_config['content_selectors'].split(';') if s.strip()],
    }
    
    print("\n转换后的爬虫配置:")
    for key, value in crawler_config.items():
        print(f"  {key}: {value}")
    
    # 验证转换结果
    expected = {
        'title_selectors': ['h1.title', '.article-title', '.news-headline'],
        'date_selectors': ['.publish-date', '.article-date', 'time'],
        'source_selectors': ['.source', '.author', '.publisher'],
        'content_selectors': ['.content', '.article-content', '.main-text'],
    }
    
    print("\n验证结果:")
    all_correct = True
    for key, expected_value in expected.items():
        actual_value = crawler_config[key]
        is_correct = actual_value == expected_value
        status = "✅" if is_correct else "❌"
        print(f"  {status} {key}: {actual_value}")
        if not is_correct:
            print(f"    期望: {expected_value}")
            all_correct = False
    
    if all_correct:
        print("\n🎉 所有配置转换正确！")
    else:
        print("\n❌ 配置转换有误")
    
    return all_correct

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n" + "=" * 60)
    print("测试向后兼容性")
    print("=" * 60)
    
    # 模拟旧的单选择器配置
    old_config = {
        'title_selector': 'h1.title',
        'date_selector': '.publish-date',
        'source_selector': '.source',
    }
    
    print("旧配置（单选择器）:")
    for key, value in old_config.items():
        print(f"  {key}: {value}")
    
    # 模拟 crawler.py 中的向后兼容逻辑
    def convert_to_multi_selectors(**kwargs):
        title_selectors = kwargs.get('title_selectors')
        date_selectors = kwargs.get('date_selectors')
        source_selectors = kwargs.get('source_selectors')
        title_selector = kwargs.get('title_selector')
        date_selector = kwargs.get('date_selector')
        source_selector = kwargs.get('source_selector')
        
        # 向后兼容：将单个选择器转换为列表
        if title_selector and not title_selectors:
            title_selectors = [title_selector]
        if date_selector and not date_selectors:
            date_selectors = [date_selector]
        if source_selector and not source_selectors:
            source_selectors = [source_selector]
        
        return {
            'title_selectors': title_selectors,
            'date_selectors': date_selectors,
            'source_selectors': source_selectors,
        }
    
    # 测试向后兼容
    result = convert_to_multi_selectors(**old_config)
    
    print("\n转换后的多选择器配置:")
    for key, value in result.items():
        print(f"  {key}: {value}")
    
    # 验证结果
    expected = {
        'title_selectors': ['h1.title'],
        'date_selectors': ['.publish-date'],
        'source_selectors': ['.source'],
    }
    
    print("\n验证结果:")
    all_correct = True
    for key, expected_value in expected.items():
        actual_value = result[key]
        is_correct = actual_value == expected_value
        status = "✅" if is_correct else "❌"
        print(f"  {status} {key}: {actual_value}")
        if not is_correct:
            print(f"    期望: {expected_value}")
            all_correct = False
    
    if all_correct:
        print("\n🎉 向后兼容性测试通过！")
    else:
        print("\n❌ 向后兼容性测试失败")
    
    return all_correct

def test_mixed_config():
    """测试混合配置（新旧混合）"""
    print("\n" + "=" * 60)
    print("测试混合配置")
    print("=" * 60)
    
    # 模拟混合配置（有些用新格式，有些用旧格式）
    mixed_config = {
        'title_selectors': ['h1.title', '.article-title'],  # 新格式
        'date_selector': '.publish-date',                   # 旧格式
        'source_selectors': ['.source', '.author'],        # 新格式
    }
    
    print("混合配置:")
    for key, value in mixed_config.items():
        print(f"  {key}: {value}")
    
    # 模拟处理逻辑
    def process_mixed_config(**kwargs):
        title_selectors = kwargs.get('title_selectors')
        date_selectors = kwargs.get('date_selectors')
        source_selectors = kwargs.get('source_selectors')
        title_selector = kwargs.get('title_selector')
        date_selector = kwargs.get('date_selector')
        source_selector = kwargs.get('source_selector')
        
        # 向后兼容：将单个选择器转换为列表
        if title_selector and not title_selectors:
            title_selectors = [title_selector]
        if date_selector and not date_selectors:
            date_selectors = [date_selector]
        if source_selector and not source_selectors:
            source_selectors = [source_selector]
        
        # 确保选择器是列表格式
        if title_selectors and not isinstance(title_selectors, list):
            title_selectors = [title_selectors]
        if date_selectors and not isinstance(date_selectors, list):
            date_selectors = [date_selectors]
        if source_selectors and not isinstance(source_selectors, list):
            source_selectors = [source_selectors]
        
        return {
            'title_selectors': title_selectors,
            'date_selectors': date_selectors,
            'source_selectors': source_selectors,
        }
    
    result = process_mixed_config(**mixed_config)
    
    print("\n处理后的配置:")
    for key, value in result.items():
        print(f"  {key}: {value}")
    
    # 验证结果
    expected = {
        'title_selectors': ['h1.title', '.article-title'],
        'date_selectors': ['.publish-date'],
        'source_selectors': ['.source', '.author'],
    }
    
    print("\n验证结果:")
    all_correct = True
    for key, expected_value in expected.items():
        actual_value = result[key]
        is_correct = actual_value == expected_value
        status = "✅" if is_correct else "❌"
        print(f"  {status} {key}: {actual_value}")
        if not is_correct:
            print(f"    期望: {expected_value}")
            all_correct = False
    
    if all_correct:
        print("\n🎉 混合配置测试通过！")
    else:
        print("\n❌ 混合配置测试失败")
    
    return all_correct

if __name__ == "__main__":
    print("开始测试多选择器功能...")
    
    test1 = test_multi_selector_config()
    test2 = test_backward_compatibility()
    test3 = test_mixed_config()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    results = [
        ("多选择器配置", test1),
        ("向后兼容性", test2),
        ("混合配置", test3),
    ]
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！多选择器功能已准备就绪。")
    else:
        print("\n❌ 部分测试失败，需要修复问题。")
    
    print("\n下一步:")
    print("1. 测试GUI界面的多选择器输入")
    print("2. 测试实际的网页抓取功能")
    print("3. 验证多选择器的优先级逻辑（第一个匹配的选择器生效）")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
翻页功能模块
处理各种不同类型网站的翻页逻辑
"""

import re
from urllib.parse import urljoin, urlparse, parse_qs, urlencode, urlunparse
from typing import Optional, Dict, Any, List, Tuple

print("Loaded pagination_handler.py from:", __file__)


class PaginationHandler:
    """翻页处理器类"""
    
    def __init__(self):
        """初始化翻页处理器"""
        self.pagination_patterns = {
            # 常见的翻页模式
            'index_n': r'index_(\d+)\.html?',
            'page_n': r'page[_-]?(\d+)\.html?',
            'p_n': r'p(\d+)\.html?',
            'list_n': r'list[_-]?(\d+)\.html?',
            'more_n': r'more[_-]?(\d+)\.html?',
            'news_n': r'news[_-]?(\d+)\.html?',
            'article_n': r'article[_-]?(\d+)\.html?',
            # 查询参数模式
            'query_page': r'[?&]page=(\d+)',
            'query_p': r'[?&]p=(\d+)',
            'query_pagenum': r'[?&]pagenum=(\d+)',
            'query_pageindex': r'[?&]pageindex=(\d+)',
            'query_currentpage': r'[?&]currentpage=(\d+)',
        }

        # 动态翻页模式配置
        self.dynamic_pagination_patterns = {
            'javascript_click': {
                'description': 'JavaScript点击翻页',
                'selectors': {
                    'next_button': ['.next', '.js_pageI', 'a[onclick*="page"]', '.pager a'],
                    'page_buttons': ['.js_pageI', '.page-num', '.pager a[href*="javascript"]'],
                    'current_page': ['.cur', '.current', '.active']
                },
                'wait_conditions': ['networkidle0', 'domcontentloaded']
            },
            'infinite_scroll': {
                'description': '无限滚动加载',
                'selectors': {
                    'load_more': ['.paged-scroll-loading', '.load-more', '.more-btn'],
                    'loading_indicator': ['.loading', '.spinner', 'img[src*="more"]'],
                    'content_container': ['#largeData', '.content-list', '.article-list']
                },
                'scroll_settings': {
                    'trigger_distance': '100px',
                    'wait_time': 1000,
                    'max_scroll_attempts': 10
                }
            },
            'dropdown_pagination': {
                'description': '下拉选择翻页',
                'selectors': {
                    'page_select': ['select[name*="page"]', '.page-select', '#pageSelect'],
                    'go_button': ['.go-btn', 'input[type="submit"]', 'button[onclick*="page"]']
                },
                'wait_conditions': ['networkidle0']
            }
        }
    
    def detect_pagination_type(self, url: str, page_suffix: str = None) -> str:
        """
        检测翻页类型

        Args:
            url: 当前页面URL
            page_suffix: 用户指定的翻页后缀

        Returns:
            翻页类型: 'suffix', 'query', 'custom', 'unknown'
        """
        if page_suffix:
            if '{n}' in page_suffix:
                # 检查是否是查询参数格式
                if '?' in page_suffix or '&' in page_suffix:
                    return 'query'
                else:
                    return 'custom'
            elif '?' in page_suffix or '&' in page_suffix:
                return 'query'
            else:
                return 'suffix'

        # 自动检测
        parsed_url = urlparse(url)

        # 检查查询参数
        if parsed_url.query:
            for pattern_name, pattern in self.pagination_patterns.items():
                if pattern_name.startswith('query_') and re.search(pattern, url):
                    return 'query'

        # 检查路径后缀
        for pattern_name, pattern in self.pagination_patterns.items():
            if not pattern_name.startswith('query_') and re.search(pattern, url):
                return 'suffix'

        return 'unknown'

    def detect_dynamic_pagination_type(self, html_content: str) -> str:
        """
        检测动态翻页类型

        Args:
            html_content: 页面HTML内容

        Returns:
            动态翻页类型: 'javascript_click', 'infinite_scroll', 'dropdown_pagination', 'unknown'
        """
        html_lower = html_content.lower()

        # 检测JavaScript点击翻页
        js_click_indicators = [
            'js_pagei',
            'javascript:',
            'onclick',
            'class="next"',
            'class="prev"',
            '上一页',
            '下一页',
            'pager'
        ]

        js_click_score = sum(1 for indicator in js_click_indicators if indicator in html_lower)

        # 检测无限滚动
        infinite_scroll_indicators = [
            'paged-scroll',
            'infinite',
            'scroll',
            'load more',
            'largedata',
            'jquery-paged-scroll',
            'handlescroll',
            'pagestoscroll'
        ]

        infinite_scroll_score = sum(1 for indicator in infinite_scroll_indicators if indicator in html_lower)

        # 检测下拉选择翻页
        dropdown_indicators = [
            '<select',
            'page-select',
            'pageselect',
            'option value',
            'go-btn'
        ]

        dropdown_score = sum(1 for indicator in dropdown_indicators if indicator in html_lower)

        # 根据得分判断类型
        scores = {
            'javascript_click': js_click_score,
            'infinite_scroll': infinite_scroll_score,
            'dropdown_pagination': dropdown_score
        }

        max_score = max(scores.values())
        if max_score == 0:
            return 'unknown'

        # 返回得分最高的类型
        for pagination_type, score in scores.items():
            if score == max_score:
                return pagination_type

        return 'unknown'

    def get_dynamic_pagination_config(self, pagination_type: str) -> Dict[str, Any]:
        """
        获取动态翻页配置

        Args:
            pagination_type: 翻页类型

        Returns:
            翻页配置字典
        """
        return self.dynamic_pagination_patterns.get(pagination_type, {})

    def extract_pagination_selectors(self, html_content: str, pagination_type: str) -> Dict[str, List[str]]:
        """
        从HTML内容中提取翻页相关的选择器

        Args:
            html_content: 页面HTML内容
            pagination_type: 翻页类型

        Returns:
            提取到的选择器字典
        """
        config = self.get_dynamic_pagination_config(pagination_type)
        if not config:
            return {}

        selectors = config.get('selectors', {})
        found_selectors = {}
        html_lower = html_content.lower()

        for selector_type, selector_list in selectors.items():
            found_selectors[selector_type] = []
            for selector in selector_list:
                # 检查选择器是否在HTML中存在
                if self._check_selector_exists(html_content, html_lower, selector):
                    found_selectors[selector_type].append(selector)

        return found_selectors

    def _check_selector_exists(self, html_content: str, html_lower: str, selector: str) -> bool:
        """
        检查选择器是否在HTML中存在

        Args:
            html_content: 原始HTML内容
            html_lower: 小写HTML内容
            selector: CSS选择器

        Returns:
            是否存在
        """
        # 简化的选择器检查逻辑
        if selector.startswith('.'):
            # 类选择器
            class_name = selector[1:]
            return f'class="{class_name}"' in html_lower or f"class='{class_name}'" in html_lower or f'class*="{class_name}"' in html_lower
        elif selector.startswith('#'):
            # ID选择器
            id_name = selector[1:]
            return f'id="{id_name}"' in html_lower or f"id='{id_name}'" in html_lower
        elif '[' in selector and ']' in selector:
            # 属性选择器
            if 'onclick' in selector:
                return 'onclick' in html_lower
            elif 'href' in selector:
                return 'href' in html_lower
            elif 'src' in selector:
                return 'src' in html_lower
            else:
                # 通用属性检查
                attr_match = re.search(r'\[([^=\]]+)', selector)
                if attr_match:
                    attr_name = attr_match.group(1)
                    return attr_name in html_lower
        else:
            # 标签选择器
            return f'<{selector}' in html_lower or f'<{selector.upper()}' in html_content

        return False
    
    def generate_page_url(self, base_url: str, page_num: int, 
                         page_suffix: str = "index_{n}.html", 
                         page_suffix_start: int = 1,
                         pagination_type: str = None) -> str:
        """
        生成指定页码的URL
        
        Args:
            base_url: 基础URL
            page_num: 页码
            page_suffix: 翻页后缀模板
            page_suffix_start: 起始页码
            pagination_type: 翻页类型
            
        Returns:
            生成的页面URL
        """
        if not base_url or page_num < 1:
            raise ValueError("无效的输入URL或页码")
        
        # 第一页通常是原始URL
        if page_num == 1:
            return base_url
        
        # 自动检测翻页类型
        if not pagination_type:
            pagination_type = self.detect_pagination_type(base_url, page_suffix)
        
        if pagination_type == 'custom':
            return self._generate_custom_url(base_url, page_num, page_suffix, page_suffix_start)
        elif pagination_type == 'query':
            return self._generate_query_url(base_url, page_num, page_suffix, page_suffix_start)
        elif pagination_type == 'suffix':
            return self._generate_suffix_url(base_url, page_num, page_suffix, page_suffix_start)
        else:
            # 默认使用自定义模式
            return self._generate_custom_url(base_url, page_num, page_suffix, page_suffix_start)
    
    def _generate_custom_url(self, base_url: str, page_num: int,
                           page_suffix: str, page_suffix_start: int) -> str:
        """生成自定义模式的URL"""
        # 计算实际页码
        actual_page_num = page_num - 1 + page_suffix_start

        # 处理{n}占位符
        suffix = page_suffix
        if "{n}" in suffix:
            suffix = suffix.format(n=actual_page_num)

        # 统一拼接逻辑
        if suffix.startswith(('http://', 'https://')):
            return suffix
        elif suffix.startswith('/'):
            # 绝对路径，直接拼接到域名后
            parsed_url = urlparse(base_url)
            return f"{parsed_url.scheme}://{parsed_url.netloc}{suffix}"
        else:
            # 相对路径，需要智能处理
            parsed_url = urlparse(base_url)
            base_path = parsed_url.path

            # 如果基础路径以.html结尾，去掉文件名保留目录
            if base_path.endswith('.html'):
                base_dir = '/'.join(base_path.split('/')[:-1])
                if base_dir:
                    return f"{parsed_url.scheme}://{parsed_url.netloc}{base_dir}/{suffix}"
                else:
                    return f"{parsed_url.scheme}://{parsed_url.netloc}/{suffix}"
            else:
                # 如果不是.html文件，直接拼接
                base_path = base_path.rstrip('/')
                return f"{parsed_url.scheme}://{parsed_url.netloc}{base_path}/{suffix}"
    
    def _generate_query_url(self, base_url: str, page_num: int,
                          page_suffix: str, page_suffix_start: int) -> str:
        """生成查询参数模式的URL"""
        parsed_url = urlparse(base_url)
        query_params = parse_qs(parsed_url.query)

        # 计算实际页码
        actual_page_num = page_num - 1 + page_suffix_start

        # 检测查询参数名
        page_param = self._detect_page_param(base_url, page_suffix)

        # 更新查询参数
        query_params[page_param] = [str(actual_page_num)]

        # 重新构建URL
        new_query = urlencode(query_params, doseq=True)
        new_url = urlunparse((
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            new_query,
            parsed_url.fragment
        ))

        return new_url
    
    def _generate_suffix_url(self, base_url: str, page_num: int,
                           page_suffix: str, page_suffix_start: int) -> str:
        """生成后缀模式的URL"""
        # 检测当前URL的模式
        pattern = self._detect_suffix_pattern(base_url)

        if pattern:
            # 计算实际页码
            actual_page_num = page_num - 1 + page_suffix_start

            # 替换页码
            new_url = re.sub(pattern, lambda m: m.group(0).replace(m.group(1), str(actual_page_num)), base_url)
            return new_url
        else:
            # 回退到自定义模式
            return self._generate_custom_url(base_url, page_num, page_suffix, page_suffix_start)
    
    def _detect_page_param(self, url: str, page_suffix: str = None) -> str:
        """检测查询参数中的页码参数名"""
        if page_suffix and ('?' in page_suffix or '&' in page_suffix):
            # 从page_suffix中提取参数名
            match = re.search(r'[?&](\w+)=', page_suffix)
            if match:
                return match.group(1)

        # 自动检测
        common_params = ['page', 'p', 'pagenum', 'pageindex', 'currentpage', 'pn']
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)

        for param in common_params:
            if param in query_params:
                return param

        # 从page_suffix中提取参数名（如果是?page={n}格式）
        if page_suffix and '{n}' in page_suffix:
            match = re.search(r'[?&](\w+)=\{n\}', page_suffix)
            if match:
                return match.group(1)

        # 默认使用page
        return 'page'
    
    def _detect_suffix_pattern(self, url: str) -> Optional[str]:
        """检测URL中的后缀模式"""
        for pattern_name, pattern in self.pagination_patterns.items():
            if not pattern_name.startswith('query_') and re.search(pattern, url):
                return pattern
        return None


# 全局翻页处理器实例
pagination_handler = PaginationHandler()


def get_page_url(input_url: str, page_num: int, 
                page_suffix: str = "index_{n}.html", 
                page_suffix_start: int = 1,
                pagination_type: str = None) -> str:
    """
    根据页码生成对应的页面URL（兼容原有接口）
    
    Args:
        input_url: 输入URL
        page_num: 页码
        page_suffix: 翻页后缀
        page_suffix_start: 起始页码
        pagination_type: 翻页类型
        
    Returns:
        生成的页面URL
    """
    return pagination_handler.generate_page_url(
        input_url, page_num, page_suffix, page_suffix_start, pagination_type
    )


def detect_pagination_patterns(urls: List[str]) -> Dict[str, Any]:
    """
    分析一组URL，检测翻页模式
    
    Args:
        urls: URL列表
        
    Returns:
        检测结果字典
    """
    results = {
        'patterns': [],
        'type': 'unknown',
        'confidence': 0.0,
        'suggested_suffix': 'index_{n}.html'
    }
    
    if not urls:
        return results
    
    # 分析每个URL
    pattern_counts = {}
    for url in urls:
        pagination_type = pagination_handler.detect_pagination_type(url)
        pattern_counts[pagination_type] = pattern_counts.get(pagination_type, 0) + 1
    
    # 确定最常见的模式
    if pattern_counts:
        most_common = max(pattern_counts.items(), key=lambda x: x[1])
        results['type'] = most_common[0]
        results['confidence'] = most_common[1] / len(urls)
    
    # 生成建议的后缀
    if results['type'] == 'query':
        # 分析查询参数
        for url in urls:
            parsed_url = urlparse(url)
            if parsed_url.query:
                page_param = pagination_handler._detect_page_param(url)
                results['suggested_suffix'] = f'?{page_param}={{n}}'
                break
    elif results['type'] == 'suffix':
        # 分析后缀模式
        for url in urls:
            pattern = pagination_handler._detect_suffix_pattern(url)
            if pattern:
                # 根据模式生成建议后缀
                if 'index_' in pattern:
                    results['suggested_suffix'] = 'index_{n}.html'
                elif 'page' in pattern:
                    results['suggested_suffix'] = 'page_{n}.html'
                elif 'list' in pattern:
                    results['suggested_suffix'] = 'list_{n}.html'
                break
    
    results['patterns'] = list(pattern_counts.keys())
    return results


# 预定义的网站翻页模式
SITE_PAGINATION_CONFIGS = {
    # 政府网站
    'gov.cn': {
        'type': 'suffix',
        'suffix': 'index_{n}.html',
        'start': 1
    },
    'people.com.cn': {
        'type': 'suffix', 
        'suffix': 'index{n}.html',
        'start': 2
    },
    'xinhuanet.com': {
        'type': 'query',
        'suffix': '?page={n}',
        'start': 1
    },
    # 新闻网站
    'sina.com.cn': {
        'type': 'query',
        'suffix': '?page={n}',
        'start': 1
    },
    'sohu.com': {
        'type': 'suffix',
        'suffix': '_{n}.shtml',
        'start': 2
    },
    # 论坛网站
    'tianya.cn': {
        'type': 'query',
        'suffix': '?pn={n}',
        'start': 1
    }
}


def get_site_pagination_config(url: str) -> Optional[Dict[str, Any]]:
    """
    根据URL获取网站的翻页配置
    
    Args:
        url: 网站URL
        
    Returns:
        翻页配置字典或None
    """
    parsed_url = urlparse(url)
    domain = parsed_url.netloc.lower()
    
    # 精确匹配
    if domain in SITE_PAGINATION_CONFIGS:
        return SITE_PAGINATION_CONFIGS[domain]
    
    # 模糊匹配
    for site_pattern, config in SITE_PAGINATION_CONFIGS.items():
        if site_pattern in domain:
            return config
    
    return None

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试翻页配置流程问题
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_config_generation():
    """测试GUI配置生成"""
    print("=" * 60)
    print("测试GUI配置生成")
    print("=" * 60)
    
    # 模拟用户在GUI中的选择
    user_selections = {
        "pagination_type": "滚动翻页",
        "enabled": True,
        "scroll_container_selector": "#largeData",
        "load_element_pattern": "#load{n}",
        "scroll_step": 1000,
        "scroll_delay": 2000,
        "max_scrolls": 20
    }
    
    print("用户在GUI中的选择:")
    for key, value in user_selections.items():
        print(f"  {key}: {value}")
    
    # 模拟 GUI 保存配置的逻辑
    pagination_config = {
        'enabled': user_selections['enabled'],
        'pagination_type': user_selections['pagination_type'],
        'scroll_container_selector': user_selections.get('scroll_container_selector', 'body'),
        'load_element_pattern': user_selections.get('load_element_pattern', ''),
        'scroll_step': user_selections.get('scroll_step', 800),
        'scroll_delay': user_selections.get('scroll_delay', 2000),
        'max_scrolls': user_selections.get('max_scrolls', 10)
    }
    
    print("\n生成的 pagination_config:")
    for key, value in pagination_config.items():
        print(f"  {key}: {value}")
    
    # 测试条件判断
    condition1 = pagination_config.get('enabled', False)
    condition2 = pagination_config.get('pagination_type') != '禁用动态翻页'
    
    print(f"\n条件判断:")
    print(f"  enabled: {condition1}")
    print(f"  pagination_type != '禁用动态翻页': {condition2}")
    print(f"  最终条件: {condition1 and condition2}")
    
    if condition1 and condition2:
        print("  ✅ 应该使用动态翻页模式")
        
        # 进一步测试翻页类型
        pagination_type = pagination_config.get('pagination_type')
        if pagination_type == '滚动翻页':
            print("  → 将调用滚动翻页功能")
            
            # 测试智能检测逻辑
            load_element_pattern = pagination_config.get('load_element_pattern', '')
            if load_element_pattern and '{n}' in load_element_pattern:
                print(f"  → 检测到加载元素模式: {load_element_pattern}")
                print("  → 将使用加载元素滚动模式")
            else:
                print("  → 将使用传统滚动模式")
        else:
            print(f"  → 未知翻页类型: {pagination_type}")
    else:
        print("  ❌ 将使用传统翻页模式")
    
    return pagination_config

def test_config_file_operations():
    """测试配置文件操作"""
    print("\n" + "=" * 60)
    print("测试配置文件操作")
    print("=" * 60)
    
    # 创建测试配置
    test_config = {
        "pagination_config": {
            "enabled": True,
            "pagination_type": "滚动翻页",
            "scroll_container_selector": "#largeData",
            "load_element_pattern": "#load{n}",
            "scroll_step": 1000,
            "scroll_delay": 2000,
            "max_scrolls": 20
        },
        "basic_config": {
            "input_url": "https://www.shrd.gov.cn/n8347/n8378/index.html",
            "base_url": "https://www.shrd.gov.cn",
            "title_selectors": ["h1", ".title", ".article-title"],
            "content_selectors": [".content", ".article-content"],
            "date_selectors": [".date", ".publish-date"],
            "source_selectors": [".source", ".author"]
        }
    }
    
    # 保存测试配置
    config_file = "test_pagination_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(test_config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 已保存测试配置到: {config_file}")
    
    # 读取配置
    with open(config_file, 'r', encoding='utf-8') as f:
        loaded_config = json.load(f)
    
    print("✅ 已读取配置文件")
    
    # 验证配置
    pagination_config = loaded_config.get('pagination_config', {})
    print(f"\n读取的 pagination_config:")
    for key, value in pagination_config.items():
        print(f"  {key}: {value}")
    
    # 测试条件判断
    condition1 = pagination_config.get('enabled', False)
    condition2 = pagination_config.get('pagination_type') != '禁用动态翻页'
    
    print(f"\n条件判断:")
    print(f"  enabled: {condition1}")
    print(f"  pagination_type != '禁用动态翻页': {condition2}")
    print(f"  最终条件: {condition1 and condition2}")
    
    if condition1 and condition2:
        print("  ✅ 配置文件中的设置正确，应该使用动态翻页")
    else:
        print("  ❌ 配置文件中的设置有问题")
    
    # 清理测试文件
    try:
        os.remove(config_file)
        print(f"✅ 已清理测试文件: {config_file}")
    except:
        pass
    
    return loaded_config

def test_crawler_thread_logic():
    """测试 CrawlerThread 逻辑"""
    print("\n" + "=" * 60)
    print("测试 CrawlerThread 逻辑")
    print("=" * 60)
    
    # 模拟不同的配置场景
    test_cases = [
        {
            "name": "正确的滚动翻页配置",
            "config": {
                "pagination_config": {
                    "enabled": True,
                    "pagination_type": "滚动翻页",
                    "load_element_pattern": "#load{n}"
                }
            },
            "expected": "动态翻页"
        },
        {
            "name": "禁用的动态翻页配置",
            "config": {
                "pagination_config": {
                    "enabled": False,
                    "pagination_type": "滚动翻页"
                }
            },
            "expected": "传统翻页"
        },
        {
            "name": "明确禁用动态翻页",
            "config": {
                "pagination_config": {
                    "enabled": True,
                    "pagination_type": "禁用动态翻页"
                }
            },
            "expected": "传统翻页"
        },
        {
            "name": "空的翻页配置",
            "config": {
                "pagination_config": {}
            },
            "expected": "传统翻页"
        },
        {
            "name": "缺少翻页配置",
            "config": {},
            "expected": "传统翻页"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试案例: {test_case['name']}")
        print("-" * 40)
        
        config = test_case['config']
        pagination_config = config.get('pagination_config', {})
        
        print(f"配置: {pagination_config}")
        
        # 模拟 CrawlerThread.run() 中的判断逻辑
        condition1 = pagination_config.get('enabled', False)
        condition2 = pagination_config.get('pagination_type') != '禁用动态翻页'
        
        print(f"enabled: {condition1}")
        print(f"pagination_type: {pagination_config.get('pagination_type')}")
        print(f"pagination_type != '禁用动态翻页': {condition2}")
        
        if condition1 and condition2:
            actual_result = "动态翻页"
            print("✅ 使用动态翻页模式，调用PaginationHandler...")
        else:
            actual_result = "传统翻页"
            print("❌ 使用传统翻页模式...")
            if not condition1:
                print("  原因: pagination_config.enabled = False")
            if not condition2:
                print(f"  原因: pagination_type = '{pagination_config.get('pagination_type')}' 等于 '禁用动态翻页'")
        
        # 验证结果
        expected_result = test_case['expected']
        is_correct = actual_result == expected_result
        status = "✅ 正确" if is_correct else "❌ 错误"
        print(f"结果: {status} (期望: {expected_result}, 实际: {actual_result})")

def main():
    """主测试函数"""
    print("开始测试翻页配置流程...")
    
    # 运行所有测试
    test_gui_config_generation()
    test_config_file_operations()
    test_crawler_thread_logic()
    
    print("\n" + "=" * 60)
    print("诊断建议")
    print("=" * 60)
    
    print("如果用户遇到 '使用传统翻页模式...' 的问题，请检查:")
    print("1. 动态翻页标签页中的 '启用动态翻页' 是否勾选")
    print("2. 翻页类型是否选择了 '滚动翻页' 而不是 '禁用动态翻页'")
    print("3. 配置是否正确保存到配置文件")
    print("4. GUI 的 get_current_config() 是否包含正确的 pagination_config")
    
    print("\n调试步骤:")
    print("1. 在 CrawlerThread.run() 中添加调试输出（已添加）")
    print("2. 检查 GUI 中动态翻页标签页的配置保存逻辑")
    print("3. 验证配置文件中的 pagination_config 内容")
    print("4. 确认 start_crawler() 传递给 CrawlerThread 的配置")

if __name__ == "__main__":
    main()

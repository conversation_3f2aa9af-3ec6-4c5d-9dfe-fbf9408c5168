from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os
import csv
import random
import requests
import txt_clear
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import selenium_diver_change
# 旧的 pagination_handler 已删除
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import queue
# Excel支持
try:
    import openpyxl
    from openpyxl import Workbook
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    print("警告: 未安装openpyxl库，Excel格式不可用。请运行: pip install openpyxl")

# 全局Excel文件锁字典和写入队列
_excel_locks = {}
_excel_queues = {}
_excel_lock = threading.Lock()  # 保护锁字典的锁

print("Loaded crawler.py from:", __file__)

def get_excel_lock(file_path):
    """获取指定Excel文件的锁"""
    with _excel_lock:
        if file_path not in _excel_locks:
            _excel_locks[file_path] = threading.Lock()
        return _excel_locks[file_path]

def safe_excel_write(file_path, data_row, headers=None):
    """线程安全的Excel写入函数"""
    if not EXCEL_AVAILABLE:
        print("错误: Excel格式不可用，请安装openpyxl库")
        return False

    file_lock = get_excel_lock(file_path)

    with file_lock:
        try:
            # 检查文件是否存在
            if os.path.exists(file_path):
                try:
                    wb = openpyxl.load_workbook(file_path)
                    ws = wb.active
                except Exception as e:
                    print(f"警告: Excel文件可能损坏，将重新创建: {e}")
                    # 创建备份文件名（避免冲突）
                    import time
                    timestamp = int(time.time())
                    backup_path = f"{file_path}.backup_{timestamp}"
                    try:
                        if os.path.exists(file_path):
                            os.rename(file_path, backup_path)
                    except Exception as backup_e:
                        print(f"备份文件失败: {backup_e}")
                    # 创建新文件
                    wb = Workbook()
                    ws = wb.active
                    if headers:
                        ws.append(headers)
            else:
                wb = Workbook()
                ws = wb.active
                if headers:
                    ws.append(headers)

            # 添加数据行
            ws.append(data_row)

            # 保存文件
            wb.save(file_path)
            return True

        except Exception as e:
            print(f"Excel文件操作失败: {e}")
            return False

# 创建保存文章的目录
def create_save_dir(page_title, export_filename=None):
    base_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "articles")
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
    if export_filename:
        # 只返回articles目录，不创建子文件夹
        return base_dir
    page_folder = "".join(c for c in page_title if c.isalnum() or c in (' ','-','_'))
    save_dir = os.path.join(base_dir, page_folder)
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    return save_dir

# 获取文章链接
def get_article_links(driver, input_url, list_container_selector, article_item_selector, list_container_type="CSS", article_item_type="CSS", title_selector=None, title_selector_type="CSS"):
    try:
        driver.get(input_url)
    except Exception as e:
        raise RuntimeError(f"无法访问目标URL: {input_url}，错误: {e}")
    page_title = driver.title
    print(f"正在爬取页面：{page_title}")
    try:
        # 支持多个列表容器
        if list_container_type == "XPath":
            main_containers = WebDriverWait(driver, 10).until(
                EC.presence_of_all_elements_located((By.XPATH, list_container_selector))
            )
        else:
            main_containers = WebDriverWait(driver, 10).until(
                EC.presence_of_all_elements_located((By.CSS_SELECTOR, list_container_selector))
            )
    except Exception as e:
        raise RuntimeError(f"页面结构异常，未找到列表容器: {list_container_selector}，错误: {e}")
    article_elements = []
    article_links = []
    article_titles = []
    for container in main_containers:
        try:
            if article_item_type == "XPath":
                articles = container.find_elements(By.XPATH, article_item_selector)
            else:
                articles = container.find_elements(By.CSS_SELECTOR, article_item_selector)
            article_elements.extend(articles)
            for article in articles:
                # 优先取 article 本身的 href
                href = article.get_attribute('href')
                # 若没有，再取其下第一个 a 标签的 href
                if not href:
                    try:
                        a_tag = article.find_element(By.TAG_NAME, 'a')
                        href = a_tag.get_attribute('href')
                    except Exception:
                        href = None
                # 如果还是没有，尝试从 onclick 提取
                if not href:
                    onclick = article.get_attribute('onclick')
                    if onclick:
                        # 兼容 onclick="location='url'" 或 onclick="window.location='url'"
                       patterns = [
                        r"location(?:\.href)?\s*=\s*['\"]([^'\"]+)",  # location='url'
                        r"window\.open\s*\(\s*['\"]([^'\"]+)",       # window.open('url'
                        r"window\.location\.href\s*=\s*['\"]([^'\"]+)",  # window.location.href='url'
                        r"redirectTo\s*\(\s*['\"]([^'\"]+)"           # 自定义函数如 redirectTo('url'
                    ]
                    
                    for pattern in patterns:
                        match = re.search(pattern, onclick)
                        if match:
                            href = match.group(1)
                            break
                # 只要有链接就加入，保证一一对应
                article_links.append(href)
                article_titles.append(article.text.strip())
        except Exception:
            continue
    print(f"找到 {len(article_links)} 个有效链接")
    return article_elements, page_title, article_links, article_titles

# 获取完整的链接
def get_full_link(href, input_url, base_url, url_mode):
    """
    url_mode: 'absolute' 绝对路径，'relative' 相对路径（base_url+href）
    """
    if not href:
        return ''
    # 绝对URL
    if href.startswith(('http://', 'https://')):
        return href
    # 协议相对URL
    if href.startswith('//'):
        scheme = base_url.split(':')[0] if ':' in base_url else 'https'
        return f"{scheme}:{href}"
    # 锚点
    if href.startswith('#'):
        return urljoin(base_url, href)
    # 相对路径
    if url_mode == "absolute":
        return urljoin(input_url, href)
    elif url_mode == "relative":
        return urljoin(base_url, href)
    else:
        return urljoin(base_url, href)
    
# 保存文章内容到文件
def save_article(
    link, save_dir, page_title, content_selectors, date_selector, source_selector,
    content_type="CSS", date_selector_type="CSS", source_selector_type="CSS", collect_links=True, mode="balance", filters=None,
    title_selector=None, title_selector_type="CSS",
    export_filename=None,  # 新增参数
    classid="",            # 新增参数，允许自定义classid
    file_format="CSV",     # 新增参数，文件格式
    retry=2,                # 新增参数，重试次数
    interval=0              # 新增参数，下载间隔（秒）
):
    """
    多线程友好：每个线程独立 session，异常自动重试，支持限速。
    """
    import threading
    session = requests.Session()
    # 判断文件名和格式
    if export_filename:
        if file_format.upper() == "EXCEL":
            filename = f"{export_filename}.xlsx"
        else:
            filename = f"{export_filename}.csv"
        file_path = os.path.join(save_dir, filename)
    else:
        if file_format.upper() == "EXCEL":
            file_path = os.path.join(save_dir, f"{page_title}.xlsx")
        else:
            file_path = os.path.join(save_dir, f"{page_title}.csv")
    city = ""
    if export_filename and "_" in export_filename:
        city = export_filename.split("_")[0]
    elif export_filename:
        city = os.path.splitext(export_filename)[0]
    article_date = ""
    article_source = ""
    img_links = []
    attach_links = []
    article_title = ""
    # --- 1. 快速模式（requests+bs4） ---
    def fetch_by_requests():
        last_exc = None
        for attempt in range(retry+1):
            try:
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                }
                resp = session.get(link, headers=headers, timeout=10)
                resp.encoding = resp.apparent_encoding
                soup = BeautifulSoup(resp.text, "html.parser")
                content = None
                for selector in content_selectors:
                    if content_type == "XPath":
                        continue
                    content = soup.select_one(selector)
                    if content:
                        break
                # 标题提取
                page_title_val = ""  # 初始化为空字符串
                if title_selector:
                    try:
                        if title_selector_type == "XPath":
                            pass
                        else:
                            title_elem = soup.select_one(title_selector)
                            if title_elem:
                                page_title_val = title_elem.get_text(strip=True)
                    except Exception:
                        pass
                page_title_val = txt_clear.clean_title(page_title_val)
                if content:
                    html = str(content)
                    text = txt_clear.clean_html_content(html)
                    imgs, attaches = [], []
                    if collect_links:
                        imgs = [img['src'] for img in content.find_all("img") if img.get("src")]
                        for a in content.find_all("a", href=True):
                            href = a['href']
                            if any(href.lower().endswith(ext) for ext in [".pdf", ".doc", ".docx", ".zip", ".rar", ".xls", ".xlsx"]):
                                attaches.append(href)
                    date, source = "", ""
                    if date_selector and date_selector_type == "CSS":
                        date_elem = soup.select_one(date_selector)
                        if date_elem:
                            date_text = date_elem.get_text(strip=True)
                            if "日期：" in date_text:
                                date = date_text.split("日期：")[-1].strip().split()[0]
                            else:
                                date = date_text
                            date = txt_clear.normalize_date(date)
                    if source_selector and source_selector_type == "CSS":
                        source_elems = soup.select(source_selector)
                        if len(source_elems) > 1:
                            source_text = source_elems[1].get_text(strip=True)
                            if "来源：" in source_text:
                                source = source_text.split("来源：")[-1].strip()
                            else:
                                source = source_text
                        elif source_elems:
                            source = source_elems[0].get_text(strip=True)
                        else:
                            source = "本站"
                        source = txt_clear.normalize_source(source)
                    return text, date, source, imgs, attaches, page_title_val
                else:
                    return None, "", "", [], [], page_title_val
            except Exception as e:
                last_exc = e
                if attempt < retry:
                    time.sleep(1)
        return None, "", "", [], [], ""
    # --- 选择模式 ---
    content_text, article_date, article_source, img_links, attach_links, article_title = fetch_by_requests()
    content_text = txt_clear.filter_content(content_text, filters)
    all_links = []
    if collect_links:
        if img_links:
            all_links.append("图片链接: " + ", ".join(img_links))
        if attach_links:
            all_links.append("附件链接: " + ", ".join(attach_links))
        if all_links and content_text:
            content_text += "[" + " | ".join(all_links) + "]"
    if not article_source:
        article_source = "本站"
    now_str = time.strftime('%Y-%m-%d %H:%M:%S')
    data_row = [
        article_date, article_source, article_title, link, content_text, classid, city, now_str
    ]
    headers = ['dateget', 'source', 'title', 'articlelink', 'dateinfo', 'classid', 'city', 'getdate']
    try:
        if file_format.upper() == "EXCEL":
            # 使用线程安全的Excel写入函数
            success = safe_excel_write(file_path, data_row, headers)
            if not success:
                return False
        else:
            write_header = not os.path.exists(file_path)
            with open(file_path, 'a', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                if write_header:
                    writer.writerow(headers)
                writer.writerow(data_row)
        print(f"已保存: {article_title}")
        return True
    except Exception as e:
        print(f"保存文章时出错: {article_title}, 错误: {str(e)}")
        return False

# 简化的翻页URL生成函数（替代旧的翻页处理模块）
def get_page_url(input_url, page_num, page_suffix, page_suffix_next=None):
    """
    根据页码生成对应的页面URL（简化版本）

    Args:
        input_url: 输入URL
        page_num: 页码
        page_suffix: 翻页后缀
        page_suffix_next: 备用翻页后缀（兼容性参数，暂未使用）

    Returns:
        生成的页面URL
    """
    from urllib.parse import urljoin, urlparse
    import re

    if page_num == 1:
        return input_url

    # 简单的翻页后缀处理
    if "{n}" in page_suffix:
        suffix = page_suffix.replace("{n}", str(page_num))
    else:
        suffix = page_suffix

    # 如果输入URL以/结尾，直接拼接
    if input_url.endswith('/'):
        return input_url + suffix
    else:
        return input_url + '/' + suffix

def handle_dynamic_pagination_crawling(driver, input_url, list_container_selector, article_item_selector,
                                     list_container_type="CSS", article_item_type="CSS",
                                     max_pages=5, pagination_type=None):
    """
    处理动态翻页爬取

    Args:
        driver: Selenium WebDriver实例
        input_url: 起始URL
        list_container_selector: 列表容器选择器
        article_item_selector: 文章项选择器
        list_container_type: 列表容器选择器类型
        article_item_type: 文章项选择器类型
        max_pages: 最大翻页数
        pagination_type: 翻页类型（可选，如果不指定则自动检测）

    Returns:
        所有页面的文章链接列表
    """
    all_articles = []

    try:
        # 首先访问页面获取HTML内容
        driver.get(input_url)
        time.sleep(2)

        # 如果没有指定翻页类型，则使用传统翻页
        if not pagination_type:
            pagination_type = 'traditional'
            print(f"使用传统翻页类型: {pagination_type}")

        if pagination_type == 'unknown' or pagination_type == 'traditional':
            print("未检测到动态翻页模式，使用传统方式爬取当前页面")
            # 使用传统方式爬取当前页面
            articles = get_article_links(driver, input_url, list_container_selector,
                                       article_item_selector, list_container_type, article_item_type)
            return articles

        # 使用动态翻页处理器
        print(f"使用动态翻页模式: {pagination_type}")

        if pagination_type == 'javascript_click':
            # JavaScript点击翻页
            for page_num in range(1, max_pages + 1):
                print(f"正在处理第 {page_num} 页...")

                # 提取当前页面的文章链接
                try:
                    articles = get_article_links(driver, driver.current_url, list_container_selector,
                                               article_item_selector, list_container_type, article_item_type)
                    all_articles.extend(articles)
                    print(f"第 {page_num} 页提取到 {len(articles)} 篇文章")
                except Exception as e:
                    print(f"第 {page_num} 页提取文章失败: {e}")

                if page_num < max_pages:
                    # 尝试点击下一页
                    if not _click_next_page_selenium(driver):
                        print(f"无法找到下一页按钮，停止在第 {page_num} 页")
                        break

                    # 等待页面加载
                    time.sleep(2)

        elif pagination_type == 'infinite_scroll':
            # 无限滚动翻页
            print("处理无限滚动翻页...")
            scroll_count = 0
            last_article_count = 0

            while scroll_count < max_pages:
                # 提取当前页面的文章链接
                try:
                    articles = get_article_links(driver, driver.current_url, list_container_selector,
                                               article_item_selector, list_container_type, article_item_type)
                    current_article_count = len(articles)

                    if current_article_count == last_article_count:
                        print("没有新内容加载，停止滚动")
                        break

                    all_articles = articles  # 无限滚动时，文章会累积在页面上
                    print(f"滚动 {scroll_count + 1} 次后，页面共有 {current_article_count} 篇文章")

                    last_article_count = current_article_count

                except Exception as e:
                    print(f"滚动 {scroll_count + 1} 次后提取文章失败: {e}")

                # 滚动到页面底部
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(3)  # 等待内容加载

                scroll_count += 1

        else:
            print(f"暂不支持的动态翻页类型: {pagination_type}")
            # 回退到传统方式
            articles = get_article_links(driver, input_url, list_container_selector,
                                       article_item_selector, list_container_type, article_item_type)
            return articles

    except Exception as e:
        print(f"动态翻页处理出错: {e}")
        # 回退到传统方式
        try:
            articles = get_article_links(driver, input_url, list_container_selector,
                                       article_item_selector, list_container_type, article_item_type)
            return articles
        except Exception as fallback_error:
            print(f"回退到传统方式也失败: {fallback_error}")
            return []

    print(f"动态翻页完成，总共提取到 {len(all_articles)} 篇文章")
    return all_articles

def _click_next_page_selenium(driver):
    """
    使用Selenium点击下一页按钮

    Args:
        driver: Selenium WebDriver实例

    Returns:
        是否成功点击
    """
    next_selectors = [
        (By.CSS_SELECTOR, '.next:not(.lose)'),  # 排除禁用的下一页按钮
        (By.CSS_SELECTOR, '.js_pageI:not(.cur)'),  # 非当前页的页码按钮
        (By.CSS_SELECTOR, '.pager a'),
        (By.XPATH, "//a[contains(text(), '下一页')]"),
        (By.XPATH, "//a[contains(text(), '下页')]"),
        (By.XPATH, "//a[contains(text(), 'Next')]"),
        (By.CSS_SELECTOR, '.next-page')
    ]

    for by_type, selector in next_selectors:
        try:
            elements = driver.find_elements(by_type, selector)
            for element in elements:
                if element.is_displayed() and element.is_enabled():
                    # 检查是否是下一页按钮（不是当前页）
                    if 'cur' not in element.get_attribute('class') and 'lose' not in element.get_attribute('class'):
                        element.click()
                        return True
        except Exception as e:
            print(f"尝试点击选择器 {selector} 失败: {e}")
            continue

    return False

def crawl_articles(input_url, base_url, max_pages=None,
                  list_container_selector=".main",
                  list_container_type="CSS",
                  article_item_selector=".clearfix.ty_list li a",
                  article_item_type="CSS",
                  title_selector=None,
                  title_selector_type="CSS",
                  content_selectors=[
                      ".article_cont",
                      "div[class*='content']",
                      "div[class*='article']",
                      "div.view.TRS_UEDITOR.trs_paper_default.trs_web",
                      ".TRS_Editor",
                      "div.zhengwen"
                  ],
                  content_type="CSS",
                  date_selector="div.xl_ly.fl > span",
                  date_selector_type="CSS",
                  source_selector="div.xl_ly.fl > span",
                  source_selector_type="CSS",
                  log_callback=None,
                  page_suffix="index_{n}.html",
                  page_suffix_start=1,
                  url_mode="absolute",
                  browser="firefox",
                  diver=None,
                  collect_links=True,   
                  mode="balance",      
                  filters=None,
                  export_filename=None, # 新增参数
                  classid="",           # 新增参数
                  file_format="CSV",    # 新增参数，文件格式
                  dynamic_pagination_type=None,  # 新增参数，动态翻页类型
                  max_workers=5,          # 新增参数，线程数
                  retry=2,                # 新增参数，重试次数
                  interval=0              # 新增参数，下载间隔（秒）
                  ):
    """
    url_mode: 'absolute' 绝对路径，'relative' 相对路径（base_url+href）
    collect_links: 是否采集正文图片和附件链接
    mode: fast（只用requests+bs4），safe（只用selenium），balance（先requests失败再selenium，默认）
    filters: 过滤关键词或正则表达式列表，匹配到的行将被移除
    export_filename: 导出文件名（全局唯一，所有文章导出到同一文件）
    """
    if log_callback:
        log_callback(f"开始爬取任务: {input_url}")
    # 处理页面加载策略
    page_load_strategy = None
    if diver and diver.get('page_load_strategy'):
        page_load_strategy = diver['page_load_strategy']
    driver = selenium_diver_change.get_driver(browser=browser, diver=diver)

    # 检查是否使用动态翻页
    if dynamic_pagination_type and dynamic_pagination_type != "traditional":
        if log_callback:
            log_callback(f"使用动态翻页模式: {dynamic_pagination_type}")
        else:
            print(f"使用动态翻页模式: {dynamic_pagination_type}")

        # 使用动态翻页处理
        try:
            all_articles = handle_dynamic_pagination_crawling(
                driver=driver,
                input_url=input_url,
                list_container_selector=list_container_selector,
                article_item_selector=article_item_selector,
                list_container_type=list_container_type,
                article_item_type=article_item_type,
                max_pages=max_pages or 5,
                pagination_type=dynamic_pagination_type
            )

            # 处理动态翻页获取的文章链接
            processed_titles = set()
            all_article_info = []
            found_urls = set()
            save_dir = create_save_dir("动态翻页结果", export_filename=export_filename)

            for i, (href, title) in enumerate(all_articles):
                if not href:
                    continue
                full_url = get_full_link(href, input_url, base_url, url_mode)

                if full_url in found_urls:
                    continue
                found_urls.add(full_url)

                if title in processed_titles:
                    continue
                processed_titles.add(title)

                # 收集文章信息，使用与传统翻页相同的格式
                all_article_info.append((title, href, save_dir, "动态翻页结果", input_url, classid))
                if log_callback:
                    log_callback(f"收集到文章 {i+1}/{len(all_articles)}: {title}")

            # 使用现有的文章处理逻辑
            success_count = 0
            fail_count = 0
            total_to_process = len(all_article_info)

            def save_one(args):
                _, href, save_dir, page_title, _, classid_val = args
                return save_article(
                    href, save_dir, page_title, content_selectors, date_selector, source_selector,
                    content_type, date_selector_type, source_selector_type, collect_links, mode, filters,
                    title_selector, title_selector_type, export_filename, classid_val, file_format,
                    retry=retry, interval=interval
                )

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = {executor.submit(save_one, info): info[0] for info in all_article_info}
                for idx, future in enumerate(as_completed(futures), 1):
                    title = futures[future]
                    try:
                        result = future.result()
                        if result:
                            success_count += 1
                        else:
                            fail_count += 1
                    except Exception as e:
                        fail_count += 1
                        if log_callback:
                            log_callback(f"处理文章时出错: {title}, 错误: {str(e)}")
                        else:
                            print(f"处理文章时出错: {title}, 错误: {str(e)}")
                    percent = (idx / total_to_process) * 100
                    status_msg = f"正在处理第{idx}/{total_to_process}篇文章 ({percent:.1f}%): {title}"
                    if log_callback:
                        log_callback(status_msg)
                    else:
                        print(status_msg)
            if log_callback:
                log_callback(f"动态翻页处理完成: 成功 {success_count} 篇，失败 {fail_count} 篇")

            driver.quit()
            return {
                'total_articles': len(all_articles),
                'processed_articles': success_count,
                'failed_articles': fail_count,
                'save_dir': save_dir if success_count > 0 else None
            }

        except Exception as e:
            if log_callback:
                log_callback(f"动态翻页处理失败，回退到传统翻页: {e}")
            else:
                print(f"动态翻页处理失败，回退到传统翻页: {e}")
            # 继续使用传统翻页方式

    # 传统翻页处理
    processed_titles = set()
    all_article_info = []
    found_urls = set()
    page_num = 1
    total_articles = 0
    while True:
        if max_pages and page_num > max_pages:
            msg = f"已达到最大页数限制: {max_pages}"
            if log_callback:
                log_callback(msg)
            else:
                print(msg)
            break
        # 使用新的翻页处理器生成页面URL
        if page_num == 1:
            page_url = input_url
        else:
            # 使用简化的翻页URL生成
            page_url = get_page_url(
                input_url=base_url,
                page_num=page_num,
                page_suffix=page_suffix
            )

        if log_callback:
            log_callback(f"[DEBUG] page_num={page_num}, page_url={page_url}")
        else:
            print(f"[DEBUG] page_num={page_num}, page_url={page_url}")
        try:
            # 根据页面加载策略决定等待方式
            if page_load_strategy == "none":
                driver.execute_script("window.stop();")
            elif page_load_strategy == "eager":
                WebDriverWait(driver, 10).until(lambda d: d.execute_script('return document.readyState') in ['interactive', 'complete'])
            else:
                WebDriverWait(driver, 15).until(lambda d: d.execute_script('return document.readyState') == 'complete')
            articles, page_title, article_links, article_titles = get_article_links(
                driver, page_url, list_container_selector, article_item_selector, list_container_type, article_item_type, title_selector, title_selector_type)
            total_articles += len(articles)
        except Exception as e:
            msg = f"获取文章链接时出错: {str(e)}"
            # 新增：遇到404直接跳过该页
            if "404" in str(e) or "Not Found" in str(e):
                msg2 = f"页面404，跳过本页: {page_url}"
                if log_callback:
                    log_callback(msg2)
                else:
                    print(msg2)
                page_num += 1
                continue
            if log_callback:
                log_callback(msg)
            else:
                print(msg)
            if "无法访问目标URL" in str(e):
                msg2 = f"请检查网络连接或目标网站是否可访问: {page_url}"
                if log_callback:
                    log_callback(msg2)
                else:
                    print(msg2)
            elif "未找到列表容器" in str(e) or "未找到文章项" in str(e):
                msg2 = f"页面结构可能已变动，请检查选择器设置: {list_container_selector} / {article_item_selector}"
                if log_callback:
                    log_callback(msg2)
                else:
                    print(msg2)
                # 新增：等待3秒后重试一次
                time.sleep(3)
                try:
                    articles, page_title, article_links, article_titles = get_article_links(
                        driver, page_url, list_container_selector, article_item_selector, list_container_type, article_item_type, title_selector, title_selector_type)
                    total_articles += len(articles)
                except Exception as e2:
                    msg3 = f"重试后依然失败: {str(e2)}"
                    if log_callback:
                        log_callback(msg3)
                    else:
                        print(msg3)
                    break
            else:
                break
        if not articles:
            msg = "没有更多文章，采集结束。"
            if log_callback:
                log_callback(msg)
            else:
                print(msg)
            break
        # 修改：如果有export_filename，save_dir为articles目录，否则为子文件夹
        save_dir = create_save_dir(page_title, export_filename=export_filename)
        new_found = False
        for article, href, title in zip(articles, article_links, article_titles):
            if not href:
                continue
            full_url = get_full_link(href, page_url, base_url, url_mode)
            found_urls.add(full_url)
            if title in processed_titles:
                continue
            # 修正：将classid加入all_article_info元组
            all_article_info.append((title, href, save_dir, page_title, page_url, classid))
            new_found = True
            processed_titles.add(title)
        if not new_found:
            msg = "没有新文章。"
            if log_callback:
                log_callback(msg)
            else:
                print(msg)
            break
        page_num += 1
    if log_callback:
        log_callback(f"共找到 {total_articles} 篇文章 ")
    else:
        print(f"共找到 {total_articles} 篇文章 ")   
    success_count = 0
    fail_count = 0
    total_to_process = len(all_article_info)
    # 多线程并发下载
    def save_one(args):
        # 修正：解包时包含classid
        title, href, save_dir, page_title, page_url, classid_val = args
        return save_article(
            href, save_dir, page_title, content_selectors, date_selector, source_selector,
            content_type, date_selector_type, source_selector_type, collect_links, mode, filters,
            title_selector, title_selector_type, export_filename, classid_val, file_format,
            retry=retry, interval=interval
        )
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {executor.submit(save_one, info): info[0] for info in all_article_info}
        for idx, future in enumerate(as_completed(futures), 1):
            title = futures[future]
            try:
                result = future.result()
                if result:
                    success_count += 1
                else:
                    fail_count += 1
            except Exception as e:
                fail_count += 1
                if log_callback:
                    log_callback(f"处理文章时出错: {title}, 错误: {str(e)}")
                else:
                    print(f"处理文章时出错: {title}, 错误: {str(e)}")
            percent = (idx / total_to_process) * 100
            status_msg = f"正在处理第{idx}/{total_to_process}篇文章 ({percent:.1f}%): {title}"
            if log_callback:
                log_callback(status_msg)
            else:
                print(status_msg)
    summary = f"下载完成！共处理 {total_to_process} 篇文章\n成功: {success_count} 篇\n失败: {fail_count} 篇"
    if log_callback:
        log_callback(summary)
    else:
        print(summary)
    return {
        "total": total_to_process,
        "success": success_count,
        "failed": fail_count
    }